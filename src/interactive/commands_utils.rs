use wl_clipboard_rs::copy::{MimeType, Options, Source};
use wl_clipboard_rs::paste;
use arboard::Clipboard;
use directories::UserDirs;
use regex::Regex;
use std::io::{Read, Write};
use std::path::PathBuf;
use std::process::{Command, Stdio};
use tokio::process::Command as TokioCommand;
use crate::interactive::app::InteractiveApp;

/// Helper function to write a file, creating parent directories if they don't exist
async fn write_file_with_dirs(file_path: &PathBuf, content: &str) -> Result<(), std::io::Error> {
    if let Some(parent) = file_path.parent() {
        tokio::fs::create_dir_all(parent).await?;
    }
    tokio::fs::write(file_path, content).await
}

/// Expands a tilde `~` in a path to the user's home directory.
pub fn expand_tilde(path_str: &str) -> PathBuf {
    if path_str == "~" {
        return UserDirs::new()
            .map(|ud| ud.home_dir().to_path_buf())
            .unwrap_or_else(|| PathBuf::from(path_str));
    }
    if path_str.starts_with("~/") {
        return UserDirs::new()
            .map(|ud| ud.home_dir().join(&path_str[2..]))
            .unwrap_or_else(|| PathBuf::from(path_str));
    }
    PathBuf::from(path_str)
}

/// Tries multiple methods to paste text from the clipboard.
pub fn paste_from_clipboard() -> Option<String> {
        // --- Attempt 1: wl-clipboard-rs crate (for Wayland) ---
        if let Ok((mut pipe, _)) = paste::get_contents(
            paste::ClipboardType::Regular,
            paste::Seat::Unspecified,
            paste::MimeType::Text,
        ) {
            let mut contents = Vec::new();
            if pipe.read_to_end(&mut contents).is_ok() {
                if let Ok(text) = String::from_utf8(contents) {
                    log::info!("Pasted from clipboard.");
                    return Some(text);
                }
            }
        }

        // Attempt 2: wl-paste shell command
        if let Ok(output) = Command::new("wl-paste").output() {
            if output.status.success() {
                let text = String::from_utf8_lossy(&output.stdout).to_string();
                    log::info!("Pasted from clipboard.");
                return Some(text);
            }
        }

    // Attempt 3: arboard (always try this as a general fallback)
    if let Ok(mut clipboard) = Clipboard::new() {
        match clipboard.get_text() {
            Ok(text) => {
                    log::info!("Pasted from clipboard.");
                return Some(text);
            }
            Err(e) => log::warn!("Failed pasting from clipboard: {:?}", e),
        }
    } else {
        log::error!("Failed to pasting from clipboard.");
    }

    log::error!("All methods to paste from clipboard failed.");
    None
}

/// Parses content from the clipboard (presumably from an LLM) and applies it to files.
pub async fn parse_and_apply_llm_edits(app: &mut InteractiveApp, content: &str) {
    // Regex to find markdown code blocks. It captures the optional language and the code.
    let re = match Regex::new(r"```[a-zA-Z0-9-]*\s*\n(?P<code>[\s\S]*?)\n```") {
        Ok(r) => r,
        Err(e) => {
            log::error!("Failed to compile regex for parsing markdown blocks: {}", e);
            return;
        }
    };

    if !re.is_match(content) {
        log::warn!("Could not find any markdown code blocks in pasted content. Expected one or more ```...``` blocks.");
        return;
    }

    let mut files_updated = 0;
    let mut files_created = 0;
    let mut files_failed = 0;

    for cap in re.captures_iter(content) {
        let block_content = &cap["code"];

        let lines: Vec<&str> = block_content.lines().collect();

        // Find the index of the first non-empty line
        let first_line_idx = match lines.iter().position(|l| !l.trim().is_empty()) {
            Some(idx) => idx,
            None => {
                log::warn!("Skipping empty or whitespace-only code block.");
                continue;
            }
        };

        let first_line = lines[first_line_idx].trim();

        // Extract path from comment
        let file_path_str = if first_line.starts_with("//") {
            first_line[2..].trim()
        } else if first_line.starts_with('#') {
            first_line[1..].trim()
        } else {
            log::warn!(
                "Skipping code block: first line is not a recognized path comment. Line was: '{}'",
                first_line
            );
            continue;
        };

        if file_path_str.is_empty() {
            log::warn!("Skipping an edit block with an empty file path in comment.");
            continue;
        }

        // The rest of the block is the code, starting from the line after the path.
        let code_lines = &lines[first_line_idx + 1..];
        let new_code = code_lines.join("\n");

        let path_expanded = expand_tilde(file_path_str);
        let full_path = if path_expanded.is_absolute() {
            path_expanded
        } else {
            app.current_path.join(path_expanded)
        };

        let was_in_context = app
            .ordered_files
            .get_all_labeled_files_map()
            .contains_key(&full_path);
        let file_existed_on_disk = full_path.exists();

        log::info!("Applying edits to {}", full_path.display());

        if let Some(parent_dir) = full_path.parent() {
            if !parent_dir.exists() {
                if let Err(e) = tokio::fs::create_dir_all(parent_dir).await {
                    log::error!(
                        "Failed to create parent directories for {}: {}",
                        full_path.display(),
                        e
                    );
                    files_failed += 1;
                    continue;
                }
            }
        }

        match write_file_with_dirs(&full_path, &new_code).await {
            Ok(_) => {
                if !file_existed_on_disk {
                    files_created += 1;
                } else {
                    files_updated += 1;
                }

                if was_in_context {
                    app.ordered_files.remove_path_recursively(&full_path);
                }

                if let Err(e) = app.ordered_files.add_path_recursively(&full_path).await {
                    log::error!(
                        "Failed to add/update file '{}' in context after writing: {}",
                        full_path.display(),
                        e
                    );
                }
            }
            Err(e) => {
                files_failed += 1;
                log::error!("Failed to write edits to {}: {}", full_path.display(), e);
            }
        }
    }
    log::info!(
        "Pasted edits applied: {} updated, {} created, {} failed.",
        files_updated,
        files_created,
        files_failed
    );
}

/// Tries multiple methods to copy text to the clipboard.
/// This function is "best effort" and will try all relevant methods.
pub fn copy_to_clipboard(content: String) {
    let is_wayland = std::env::var("WAYLAND_DISPLAY").is_ok();

    if is_wayland {
        // --- Attempt 1: wl-clipboard-rs crate (for Wayland) ---
        let opts = Options::new();
        opts.copy(
            Source::Bytes(content.clone().into_bytes().into()),
            MimeType::Autodetect,
        )
        .is_ok();

        // Attempt 2: wl-copy shell command
        if let Ok(mut child) = Command::new("wl-copy")
            .stdin(Stdio::piped())
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .spawn()
        {
            if let Some(mut stdin) = child.stdin.take() {
                let _ = stdin.write_all(content.as_bytes());
            }
        }
    }

    // Attempt 3: arboard (always try this as a general fallback)
    if let Ok(mut clipboard) = Clipboard::new() {
        if clipboard.set_text(content).is_ok() {
            log::info!("Content copied successfully to clipboard.");
        } else {
            log::info!("Copy to clipboard failed.");
        }
    } else {
        log::warn!("Clipboard copy failed.");
    }
}

/// Generates the context string for clipboard operations, with optional diffs and test output.
pub async fn generate_context_string(
    app: &InteractiveApp,
    include_diffs: bool,
    include_test_output: bool,
) -> String {
    let mut context_string = String::new();

    // 1. Files In Context
    context_string.push_str("# Files In Context\n\n");
    let files_map = app.ordered_files.get_all_labeled_files_map();
    if files_map.is_empty() {
        context_string.push_str("No files in context.\n\n");
    } else {
        for (path, labeled_file) in files_map.iter() {
            let file_path = path.display();
            context_string.push_str(&format!("## {}\n", file_path));
            context_string.push_str("```\n");
            let content = labeled_file.get_original_lines_string();
            context_string.push_str(&content);
            if !content.ends_with('\n') {
                context_string.push('\n');
            }
            context_string.push_str("```\n\n");
        }
    }

    // 2. Previous Task Info
    if let Some(previous_task) = app.tasks.iter().last() {
        if let Some(task_info) = &previous_task.initial_task_info {
            if !task_info.is_empty() {
                context_string.push_str("# Previous Task Extra Info\n");
                context_string.push_str("```\n");
                context_string.push_str(task_info);
                if !task_info.ends_with('\n') {
                    context_string.push('\n');
                }
                context_string.push_str("```\n\n");
            }
        }
        context_string.push_str("# Original User Message For Prevous Task\n");
        context_string.push_str("```\n");
        context_string.push_str(&previous_task.initial_user_prompt);
        if !previous_task.initial_user_prompt.ends_with('\n') {
            context_string.push('\n');
        }
        context_string.push_str("```\n\n");
    }

    let mut sections_to_add = String::new();

    // 3. Git Diffs (if requested)
    if include_diffs {
        let file_paths: Vec<PathBuf> = files_map.keys().cloned().collect();
        if !file_paths.is_empty() {
            let mut command = TokioCommand::new("git");
            command.arg("diff").arg("--");
            for path in &file_paths {
                if let Ok(relative_path) = path.strip_prefix(&app.current_path) {
                    command.arg(relative_path);
                } else {
                    command.arg(path);
                }
            }
            command.current_dir(&app.current_path);

            if let Ok(output) = command.output().await {
                let diff_output = String::from_utf8_lossy(&output.stdout);
                if !diff_output.trim().is_empty() {
                    sections_to_add.push_str("# Git Diffs\n\n");
                    sections_to_add.push_str("```diff\n");
                    sections_to_add.push_str(&diff_output);
                    if !diff_output.ends_with('\n') {
                        sections_to_add.push('\n');
                    }
                    sections_to_add.push_str("```\n\n");
                }
            }
        }
    }

    // 4. Test Output (if requested)
    if include_test_output && !app.app_config.auto_test_command.is_empty() {
        let command_str = app.app_config.auto_test_command.clone();
        if let Ok(output) = TokioCommand::new("sh")
            .arg("-c")
            .arg(&command_str)
            .current_dir(&app.current_path)
            .output()
            .await
        {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            let combined_output = if !stdout.is_empty() && !stderr.is_empty() {
                format!(
                    "--- STDOUT ---\n{}\n--- STDERR ---\n{}",
                    stdout.trim_end(),
                    stderr.trim_end()
                )
            } else if !stdout.is_empty() {
                stdout.trim_end().to_string()
            } else if !stderr.is_empty() {
                format!("--- STDERR ---\n{}", stderr.trim_end())
            } else {
                "<no output>".to_string()
            };

            sections_to_add.push_str("# Compile/Test Output\n\n");
            sections_to_add.push_str("```\n");
            sections_to_add.push_str(&combined_output);
            if !combined_output.ends_with('\n') {
                sections_to_add.push('\n');
            }
            sections_to_add.push_str("```\n\n");
        }
    }

    context_string.push_str(&sections_to_add);

    // 5. AI Goal
    context_string.push_str("# Your Current Goal As An AI\n\n");
    context_string.push_str("Your current duty is to analyze all of the files in context extremely carefully, including all information provided about the previous/current task, and return full files rewritten with new updates to accomplish the stated task. Only return files that you are updating with new edits.\n\n");
    context_string.push_str("For each file you want to update, return a single markdown code block with the language specifier. The very first line inside the code block **must** be a comment containing only the file path. For example:\n");
    context_string.push_str("```rust\n");
    context_string.push_str("// src/main.rs\n");
    context_string.push_str("fn main() {\n");
    context_string.push_str("    println!(\"Hello, new world!\");\n");
    context_string.push_str("}\n");
    context_string.push_str("```\n\n");
    context_string.push_str("The rest of the block must contain the **FULL** code of the file from top to bottom, rewritten with your changes applied.\n\n");
    context_string.push_str("You **MUST** include the **FULL** code of each file you are updating. NEVER use elipses, diffs, or partial replacements. Each code block must be a complete, rewritten file.\n\n");
    context_string.push_str("The current task that you need to accomplish is:\n\n");

    context_string
}
