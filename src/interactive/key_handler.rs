use crate::editor::ProcessingSignal;
use crate::interactive::app::{InputMode, InteractiveApp};
use crate::interactive::async_ops;
use crate::Task;
use crossterm::event::{KeyCode, KeyEvent, KeyModifiers};
use std::path::PathBuf;
use tokio::sync::mpsc; // For spawning new tasks

// Helper function to convert char index to byte index for UTF-8 strings
fn char_idx_to_byte_idx(s: &str, char_idx: usize) -> usize {
    s.char_indices()
        .nth(char_idx)
        .map(|(byte_idx, _)| byte_idx)
        .unwrap_or_else(|| s.len())
}

#[allow(clippy::too_many_lines)] // The nature of key handling can lead to many lines
pub async fn handle_key_event(
    app: &mut InteractiveApp,
    key_event: KeyEvent,
    file_search_results_tx: &mpsc::Sender<Vec<PathBuf>>, // For file suggester
    processing_tx: &mpsc::Sender<ProcessingSignal>,      // For submitting tasks
) {
    match app.input_mode {
        InputMode::Normal => {
            match key_event.code {
                KeyCode::Enter => {
                    if app.file_suggester_state.active {
                        if let Some(completed_input_line) = app
                            .file_suggester_state
                            .get_current_selected_path_for_input()
                        {
                            app.input = completed_input_line;
                            if !app.input.ends_with(std::path::MAIN_SEPARATOR)
                                && !app.input.ends_with('/')
                                && !app.input.ends_with(' ')
                            {
                                app.input.push(' ');
                            }
                            app.input_cursor_char_idx = app.input.chars().count();
                            app.file_suggester_state.deactivate();
                        }
                    } else if app.autocomplete_state.active {
                        if let Some(selected_command_name) =
                            app.autocomplete_state.get_current_selection_text()
                        {
                            app.input = selected_command_name;
                            // Deactivate other suggesters explicitly here before potential activation by command
                            app.file_suggester_state.deactivate();
                            app.model_suggester_state.deactivate();
                            app.auto_research_mode_suggester_state.deactivate();
                            app.auto_expert_switch_suggester_state.deactivate();
                            if crate::interactive::commands::COMMAND_DEFINITIONS
                                .iter()
                                .any(|cmd| cmd.name == app.input && cmd.requires_argument)
                            {
                                app.input.push(' ');
                            }
                            app.input_cursor_char_idx = app.input.chars().count();
                            app.autocomplete_state.deactivate();

                            // NEW: Activate subsequent suggesters if applicable
                            let completed_command_prefix = app.input.clone();
                            let empty_arg_part = "";

                            if completed_command_prefix == "/add " {
                                app.file_suggester_state.activate_or_update(
                                    app.current_path.clone(),
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    file_search_results_tx.clone(),
                                );
                            } else if completed_command_prefix == "/expert-model-set " {
                                app.model_suggester_state.activate_or_update(
                                    &app.app_config.models_list,
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    true,                             // include_default_option
                                );
                            } else if completed_command_prefix == "/research-model-set " {
                                app.model_suggester_state.activate_or_update(
                                    &app.app_config.models_list,
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    true,                             // include_default_option
                                );
                            } else if completed_command_prefix == "/retry-model-set " {
                                app.model_suggester_state.activate_or_update(
                                    &app.app_config.models_list,
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    true,                             // include_default_option
                                );
                            } else if completed_command_prefix == "/summary-model-set " {
                                app.model_suggester_state.activate_or_update(
                                    &app.app_config.models_list,
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    true,                             // include_default_option
                                );
                            } else if completed_command_prefix == "/decision-model-set " {
                                app.model_suggester_state.activate_or_update(
                                    &app.app_config.models_list,
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    true,                             // include_default_option
                                );
                            } else if completed_command_prefix == "/default-model-set " {
                                app.model_suggester_state.activate_or_update(
                                    &app.app_config.models_list,
                                    empty_arg_part,
                                    completed_command_prefix.clone(), // Clone here
                                    false,                            // include_default_option
                                );
                            } else if completed_command_prefix == "/auto-research " {
                                app.auto_research_mode_suggester_state.activate_or_update(
                                    empty_arg_part,
                                    completed_command_prefix.clone(),
                                ); // Clone here
                            } else if completed_command_prefix == "/auto-expert " {
                                app.auto_expert_switch_suggester_state
                                    .activate_or_update(empty_arg_part, completed_command_prefix);
                            }
                            // END NEW
                        }
                    } else if app.model_suggester_state.active {
                        if let Some(selected_alias) =
                            app.model_suggester_state.get_current_selected_alias_only()
                        {
                            // The input should be "/default-model-set "
                            // We append the selected alias.
                            app.input = format!(
                                "{} {}",
                                app.model_suggester_state
                                    .input_prefix_for_completion
                                    .trim_end(),
                                selected_alias
                            );
                            app.input_cursor_char_idx = app.input.chars().count();
                            app.model_suggester_state.deactivate();
                            // Now, let the command handler process this completed command.
                            // The subsequent call to handle_command_if_present will take care of it.
                        }
                    } else if app.auto_research_mode_suggester_state.active {
                        if let Some(selected_mode_name) = app
                            .auto_research_mode_suggester_state
                            .get_current_selected_name_for_input()
                        {
                            app.input = format!(
                                "{} {}",
                                app.auto_research_mode_suggester_state
                                    .input_prefix_for_completion
                                    .trim_end(),
                                selected_mode_name
                            );
                            app.input_cursor_char_idx = app.input.chars().count();
                            app.auto_research_mode_suggester_state.deactivate();
                        }
                    } else if app.auto_expert_switch_suggester_state.active {
                        if let Some(selected_switch_name) = app
                            .auto_expert_switch_suggester_state
                            .get_current_selected_name_for_input()
                        {
                            app.input = format!(
                                "{} {}",
                                app.auto_expert_switch_suggester_state
                                    .input_prefix_for_completion
                                    .trim_end(),
                                selected_switch_name
                            );
                            app.input_cursor_char_idx = app.input.chars().count();
                            app.auto_expert_switch_suggester_state.deactivate();
                        }
                    } else {
                        // This 'else' means no suggester was active, so Enter means submit.
                        // It is crucial that this block executes if no suggester handled 'Enter'.
                        // It contains the logic for submitting commands or prompts.
                        // If a model was selected from model_suggester, app.input is now a full command.
                        if !app.input.is_empty() {
                            // Deactivate all suggesters before processing the input as a command or prompt
                            app.file_suggester_state.deactivate();
                            app.autocomplete_state.deactivate();
                            app.model_suggester_state.deactivate();
                            app.auto_research_mode_suggester_state.deactivate();
                            app.auto_expert_switch_suggester_state.deactivate();

                            // Reload files before processing any input to ensure freshness
                            let _changed_files = app.ordered_files.refresh_all_files_from_filesystem().await;

                            if crate::interactive::commands::handle_command_if_present(
                                app,
                                processing_tx,
                            )
                            .await
                            { // Pass processing_tx
                                 // Command was handled, loop will redraw and re-select.
                                 // app.should_quit is checked by the main loop in runner.rs
                                 // app.last_significant_command is set by handle_command_if_present for relevant commands.
                            } else {
                                // Not a command, so it's a prompt for the LLM
                                app.last_significant_command =
                                    Some(crate::interactive::app::LastSignificantCommandType::Edit);
                                let pure_user_request = app.input.clone();
                                app.task_retry_count = 0;

                                app.submit_current_input(); // Sets app.is_processing, app_config.user_prompt, logs, and sends notification

                                let mut new_task_for_user_input = Task::new(
                                    pure_user_request.clone(),
                                    app.app_config.task_info.clone(),
                                );
                                let (files_msg_opt, _included_content) =
                                    app // _included_content not stored on Task
                                        .ordered_files
                                        .get_files_for_prompt_message(&app.current_path); // Use OrderedFiles method
                                if let Some(files_msg) = files_msg_opt {
                                    new_task_for_user_input.add_message(files_msg);
                                }
                                let mut instruction_content_user = pure_user_request;
                                if let Some(ti_content) = &new_task_for_user_input.initial_task_info
                                {
                                    if !ti_content.trim().is_empty() {
                                        instruction_content_user = format!(
                                            "Important task context:\n---\n{}\n---\n\n{}",
                                            ti_content.trim(),
                                            instruction_content_user
                                        );
                                    }
                                }
                                new_task_for_user_input.add_message(crate::llm::ChatMessage {
                                    role: crate::llm::ChatRole::User,
                                    content: instruction_content_user,
                                    message_type: crate::llm::MessageType::Text,
                                });
                                app.tasks.push(new_task_for_user_input.clone());

                                // Get all tasks *before* the new_task_for_user_input was added
                                let all_previous_tasks_for_processing: Vec<Task> = app
                                    .tasks
                                    .iter()
                                    .take(app.tasks.len() - 1) // All tasks except the one just added
                                    .cloned()
                                    .collect();
                                let task_app_config_user = app.app_config.clone();
                                let task_ordered_files_snapshot = app.ordered_files.clone(); // Clone OrderedFiles

                                app.input.clear();
                                app.input_cursor_char_idx = 0;
                                app.file_suggester_state.deactivate();
                                app.autocomplete_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();

                                // The new_task_for_user_input object was added to app.tasks.
                                // Its messages field contains the files_msg (if any) and the user instruction.
                                // These are the initial_messages_for_new_task.
                                let initial_messages_for_processing =
                                    new_task_for_user_input.messages.clone();

                                // The Task object passed to spawn_new_task_processing should be the one from app.tasks.
                                // We pass its clone, but its .messages field will be populated by run_edit_cycle's return.
                                // So, the new_task_for_user_input that was pushed to app.tasks is correct.
                                // We pass a clone of it to spawn_new_task_processing.
                                // The `current_task_for_run` in spawn_new_task_processing will be this clone.
                                // Its `messages` field will be overwritten by the result of `run_edit_cycle`.

                                let abort_handle = async_ops::spawn_new_edit_processing(
                                    task_app_config_user,
                                    new_task_for_user_input.clone(), // Pass a clone of the task object (whose messages will be replaced)
                                    app.pruned_tasks_summary_messages.clone(),
                                    all_previous_tasks_for_processing,
                                    initial_messages_for_processing, // Pass the specifically prepared initial messages
                                    task_ordered_files_snapshot,
                                    app.current_path.clone(), // Pass current_path
                                    processing_tx.clone(),
                                    0,    // is_retry
                                    None, // forced_research_content
                                );
                                app.current_task_abort_handle = Some(abort_handle);
                            }
                        }
                    }
                } // Correctly closes the KeyCode::Enter block
                KeyCode::Char(c) => {
                    let byte_idx = char_idx_to_byte_idx(&app.input, app.input_cursor_char_idx);
                    app.input.insert(byte_idx, c);
                    app.input_cursor_char_idx += 1;
                    app.current_history_index = None;

                    if app.input.starts_with("/add ") {
                        app.autocomplete_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/add ".len();
                        let args_str = &app.input[command_prefix_len..];
                        let last_segment_start_in_args = args_str.rfind(' ').map_or(0, |i| i + 1);
                        let current_segment_being_typed = &args_str[last_segment_start_in_args..];
                        let prefix_for_current_segment = app.input
                            [..(command_prefix_len + last_segment_start_in_args)]
                            .to_string();
                        app.file_suggester_state.activate_or_update(
                            app.current_path.clone(),
                            current_segment_being_typed,
                            prefix_for_current_segment,
                            file_search_results_tx.clone(),
                        );
                    } else if app.input.starts_with("/drop ") {
                        app.autocomplete_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/drop ".len();
                        let args_str = &app.input[command_prefix_len..];
                        let last_segment_start_in_args = args_str.rfind(' ').map_or(0, |i| i + 1);
                        let current_segment_being_typed = &args_str[last_segment_start_in_args..];
                        let prefix_for_current_segment = app.input
                            [..(command_prefix_len + last_segment_start_in_args)]
                            .to_string();

                        // Get existing file paths
                        let existing_files = app.ordered_files.get_paths_for_ui_display();

                        // Call the new activator
                        app.file_suggester_state.activate_from_context_files(
                            &existing_files,
                            &app.current_path,
                            current_segment_being_typed,
                            prefix_for_current_segment,
                        );
                    } else if app.input.starts_with("/expert-model-set ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/expert-model-set ".len();
                        let alias_part = &app.input[command_prefix_len..];
                        app.model_suggester_state.activate_or_update(
                            &app.app_config.models_list,
                            alias_part,
                            app.input[..command_prefix_len].to_string(),
                            true, // include_default_option
                        );
                    } else if app.input.starts_with("/research-model-set ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/research-model-set ".len();
                        let alias_part = &app.input[command_prefix_len..];
                        app.model_suggester_state.activate_or_update(
                            &app.app_config.models_list,
                            alias_part,
                            app.input[..command_prefix_len].to_string(),
                            true, // include_default_option
                        );
                    } else if app.input.starts_with("/retry-model-set ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/retry-model-set ".len();
                        let alias_part = &app.input[command_prefix_len..];
                        app.model_suggester_state.activate_or_update(
                            &app.app_config.models_list,
                            alias_part,
                            app.input[..command_prefix_len].to_string(),
                            true, // include_default_option
                        );
                    } else if app.input.starts_with("/summary-model-set ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/summary-model-set ".len();
                        let alias_part = &app.input[command_prefix_len..];
                        app.model_suggester_state.activate_or_update(
                            &app.app_config.models_list,
                            alias_part,
                            app.input[..command_prefix_len].to_string(),
                            true, // include_default_option
                        );
                    } else if app.input.starts_with("/decision-model-set ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/decision-model-set ".len();
                        let alias_part = &app.input[command_prefix_len..];
                        app.model_suggester_state.activate_or_update(
                            &app.app_config.models_list,
                            alias_part,
                            app.input[..command_prefix_len].to_string(),
                            true, // include_default_option
                        );
                    } else if app.input.starts_with("/default-model-set ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/default-model-set ".len();
                        let alias_part = &app.input[command_prefix_len..];
                        app.model_suggester_state.activate_or_update(
                            &app.app_config.models_list,
                            alias_part,
                            app.input[..command_prefix_len].to_string(),
                            false, // include_default_option
                        );
                    } else if app.input.starts_with("/auto-research ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        let command_prefix_len = "/auto-research ".len();
                        let mode_part = &app.input[command_prefix_len..];
                        app.auto_research_mode_suggester_state.activate_or_update(
                            mode_part,
                            app.input[..command_prefix_len].to_string(),
                        );
                    } else if app.input.starts_with("/auto-expert ") {
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        let command_prefix_len = "/auto-expert ".len();
                        let switch_part = &app.input[command_prefix_len..];
                        app.auto_expert_switch_suggester_state.activate_or_update(
                            switch_part,
                            app.input[..command_prefix_len].to_string(),
                        );
                    } else {
                        // General command autocomplete or normal input
                        app.file_suggester_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                        if app.input.starts_with('/') && !app.input.contains(char::is_whitespace) {
                            if !app.autocomplete_state.active {
                                app.autocomplete_state.activate(&app.input);
                            } else {
                                app.autocomplete_state.filter_suggestions(&app.input);
                            }
                        } else {
                            app.autocomplete_state.deactivate();
                        }
                    }
                }
                KeyCode::Backspace => {
                    if app.input_cursor_char_idx > 0 {
                        let char_idx_to_remove = app.input_cursor_char_idx - 1;
                        let byte_idx = char_idx_to_byte_idx(&app.input, char_idx_to_remove);
                        if byte_idx < app.input.len() {
                            // Ensure byte_idx is valid
                            app.input.remove(byte_idx);
                            app.input_cursor_char_idx -= 1;
                            app.current_history_index = None;

                            if app.input.starts_with("/add ") {
                                app.autocomplete_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/add ".len();
                                // Logic for /add file suggester
                                if app.input.len() >= command_prefix_len {
                                    let args_str = &app.input[command_prefix_len..];
                                    let last_segment_start_in_args =
                                        args_str.rfind(' ').map_or(0, |i| i + 1);
                                    let current_segment_being_typed =
                                        &args_str[last_segment_start_in_args..];
                                    let prefix_for_current_segment = app.input
                                        [..(command_prefix_len + last_segment_start_in_args)]
                                        .to_string();
                                    app.file_suggester_state.activate_or_update(
                                        app.current_path.clone(),
                                        current_segment_being_typed,
                                        prefix_for_current_segment,
                                        file_search_results_tx.clone(),
                                    );
                                } else {
                                    app.file_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/drop ") {
                                app.autocomplete_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/drop ".len();
                                if app.input.len() >= command_prefix_len {
                                    let args_str = &app.input[command_prefix_len..];
                                    let last_segment_start_in_args =
                                        args_str.rfind(' ').map_or(0, |i| i + 1);
                                    let current_segment_being_typed =
                                        &args_str[last_segment_start_in_args..];
                                    let prefix_for_current_segment = app.input
                                        [..(command_prefix_len + last_segment_start_in_args)]
                                        .to_string();

                                    let existing_files = app.ordered_files.get_paths_for_ui_display();
                                    app.file_suggester_state.activate_from_context_files(
                                        &existing_files,
                                        &app.current_path,
                                        current_segment_being_typed,
                                        prefix_for_current_segment,
                                    );
                                } else {
                                    app.file_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/expert-model-set ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/expert-model-set ".len();
                                if app.input.len() >= command_prefix_len {
                                    let alias_part = &app.input[command_prefix_len..];
                                    app.model_suggester_state.activate_or_update(
                                        &app.app_config.models_list,
                                        alias_part,
                                        app.input[..command_prefix_len].to_string(),
                                        true, // include_default_option
                                    );
                                } else {
                                    app.model_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/research-model-set ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/research-model-set ".len();
                                if app.input.len() >= command_prefix_len {
                                    let alias_part = &app.input[command_prefix_len..];
                                    app.model_suggester_state.activate_or_update(
                                        &app.app_config.models_list,
                                        alias_part,
                                        app.input[..command_prefix_len].to_string(),
                                        true, // include_default_option
                                    );
                                } else {
                                    app.model_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/retry-model-set ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/retry-model-set ".len();
                                if app.input.len() >= command_prefix_len {
                                    let alias_part = &app.input[command_prefix_len..];
                                    app.model_suggester_state.activate_or_update(
                                        &app.app_config.models_list,
                                        alias_part,
                                        app.input[..command_prefix_len].to_string(),
                                        true, // include_default_option
                                    );
                                } else {
                                    app.model_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/summary-model-set ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/summary-model-set ".len();
                                if app.input.len() >= command_prefix_len {
                                    let alias_part = &app.input[command_prefix_len..];
                                    app.model_suggester_state.activate_or_update(
                                        &app.app_config.models_list,
                                        alias_part,
                                        app.input[..command_prefix_len].to_string(),
                                        true, // include_default_option
                                    );
                                } else {
                                    app.model_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/decision-model-set ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/decision-model-set ".len();
                                if app.input.len() >= command_prefix_len {
                                    let alias_part = &app.input[command_prefix_len..];
                                    app.model_suggester_state.activate_or_update(
                                        &app.app_config.models_list,
                                        alias_part,
                                        app.input[..command_prefix_len].to_string(),
                                        true, // include_default_option
                                    );
                                } else {
                                    app.model_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/default-model-set ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/default-model-set ".len();
                                if app.input.len() >= command_prefix_len {
                                    let alias_part = &app.input[command_prefix_len..];
                                    app.model_suggester_state.activate_or_update(
                                        &app.app_config.models_list,
                                        alias_part,
                                        app.input[..command_prefix_len].to_string(),
                                        false, // include_default_option
                                    );
                                } else {
                                    app.model_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/auto-research ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                let command_prefix_len = "/auto-research ".len();
                                if app.input.len() >= command_prefix_len {
                                    let mode_part = &app.input[command_prefix_len..];
                                    app.auto_research_mode_suggester_state.activate_or_update(
                                        mode_part,
                                        app.input[..command_prefix_len].to_string(),
                                    );
                                } else {
                                    app.auto_research_mode_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/auto-expert ") {
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                let command_prefix_len = "/auto-expert ".len();
                                if app.input.len() >= command_prefix_len {
                                    let switch_part = &app.input[command_prefix_len..];
                                    app.auto_expert_switch_suggester_state.activate_or_update(
                                        switch_part,
                                        app.input[..command_prefix_len].to_string(),
                                    );
                                } else {
                                    app.auto_expert_switch_suggester_state.deactivate();
                                }
                            } else if app.input.starts_with("/")
                                && !app.input.contains(char::is_whitespace)
                            {
                                // General command autocomplete
                                app.file_suggester_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                                app.autocomplete_state.filter_suggestions(&app.input);
                                if !app.autocomplete_state.active
                                    && !app.autocomplete_state.filtered_suggestions.is_empty()
                                {
                                    app.autocomplete_state.activate(&app.input);
                                } else if app.autocomplete_state.filtered_suggestions.is_empty() {
                                    app.autocomplete_state.deactivate();
                                }
                            } else {
                                // Not a command prefix, or empty input
                                app.autocomplete_state.deactivate();
                                app.file_suggester_state.deactivate();
                                app.model_suggester_state.deactivate();
                                app.auto_research_mode_suggester_state.deactivate();
                                app.auto_expert_switch_suggester_state.deactivate();
                            }
                        }
                    }
                }
                KeyCode::Left => {
                    if app.input_cursor_char_idx > 0 {
                        app.input_cursor_char_idx -= 1;
                    }
                }
                KeyCode::Right => {
                    if app.input_cursor_char_idx < app.input.chars().count() {
                        // Use chars().count() for UTF-8
                        app.input_cursor_char_idx += 1;
                    }
                }
                KeyCode::Up => {
                    if app.file_suggester_state.active {
                        app.file_suggester_state.select_previous();
                    } else if app.autocomplete_state.active {
                        app.autocomplete_state.select_previous();
                    } else if app.model_suggester_state.active {
                        app.model_suggester_state.select_previous();
                    } else if app.auto_research_mode_suggester_state.active {
                        app.auto_research_mode_suggester_state.select_previous();
                    } else if app.auto_expert_switch_suggester_state.active {
                        app.auto_expert_switch_suggester_state.select_previous();
                    } else if !app.input_history.is_empty() {
                        match app.current_history_index {
                            None => {
                                if !app.input.is_empty()
                                    && app.input_history.last() != Some(&app.input)
                                {
                                    app.current_input_draft = app.input.clone();
                                } else if app.input.is_empty() {
                                    app.current_input_draft.clear();
                                }
                                let new_idx = app.input_history.len() - 1;
                                app.input = app.input_history[new_idx].clone();
                                app.current_history_index = Some(new_idx);
                                app.input_cursor_char_idx = app.input.chars().count();
                            }
                            Some(idx) => {
                                if idx > 0 {
                                    let new_idx = idx - 1;
                                    app.input = app.input_history[new_idx].clone();
                                    app.current_history_index = Some(new_idx);
                                    app.input_cursor_char_idx = app.input.chars().count();
                                }
                            }
                        }
                        // Deactivate suggesters if navigating history
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                    }
                }
                KeyCode::Down => {
                    if app.file_suggester_state.active {
                        app.file_suggester_state.select_next();
                    } else if app.autocomplete_state.active {
                        app.autocomplete_state.select_next();
                    } else if app.model_suggester_state.active {
                        app.model_suggester_state.select_next();
                    } else if app.auto_research_mode_suggester_state.active {
                        app.auto_research_mode_suggester_state.select_next();
                    } else if app.auto_expert_switch_suggester_state.active {
                        app.auto_expert_switch_suggester_state.select_next();
                    } else {
                        match app.current_history_index {
                            Some(idx) => {
                                if idx < app.input_history.len() - 1 {
                                    let new_idx = idx + 1;
                                    app.input = app.input_history[new_idx].clone();
                                    app.current_history_index = Some(new_idx);
                                    app.input_cursor_char_idx = app.input.chars().count();
                                } else {
                                    app.input = app.current_input_draft.clone();
                                    app.current_history_index = None;
                                    app.input_cursor_char_idx = app.input.chars().count();
                                }
                            }
                            None => { /* Do nothing */ }
                        }
                        // Deactivate suggesters if navigating history
                        app.autocomplete_state.deactivate();
                        app.file_suggester_state.deactivate();
                        app.model_suggester_state.deactivate();
                        app.auto_research_mode_suggester_state.deactivate();
                        app.auto_expert_switch_suggester_state.deactivate();
                    }
                }
                KeyCode::BackTab => {
                    // Shift+Tab
                    if app.file_suggester_state.active {
                        app.file_suggester_state.select_previous();
                    } else if app.autocomplete_state.active {
                        app.autocomplete_state.select_previous();
                    } else if app.model_suggester_state.active {
                        app.model_suggester_state.select_previous();
                    } else if app.auto_research_mode_suggester_state.active {
                        app.auto_research_mode_suggester_state.select_previous();
                    } else if app.auto_expert_switch_suggester_state.active {
                        app.auto_expert_switch_suggester_state.select_previous();
                    }
                }
                KeyCode::Tab => {
                    if app.file_suggester_state.active {
                        if key_event.modifiers.contains(KeyModifiers::SHIFT) {
                            app.file_suggester_state.select_previous();
                        } else {
                            app.file_suggester_state.select_next();
                        }
                    } else if app.autocomplete_state.active {
                        if key_event.modifiers.contains(KeyModifiers::SHIFT) {
                            app.autocomplete_state.select_previous();
                        } else {
                            app.autocomplete_state.select_next();
                        }
                    } else if app.model_suggester_state.active {
                        if key_event.modifiers.contains(KeyModifiers::SHIFT) {
                            app.model_suggester_state.select_previous();
                        } else {
                            app.model_suggester_state.select_next();
                        }
                    } else if app.auto_research_mode_suggester_state.active {
                        if key_event.modifiers.contains(KeyModifiers::SHIFT) {
                            app.auto_research_mode_suggester_state.select_previous();
                        } else {
                            app.auto_research_mode_suggester_state.select_next();
                        }
                    } else if app.auto_expert_switch_suggester_state.active {
                        if key_event.modifiers.contains(KeyModifiers::SHIFT) {
                            app.auto_expert_switch_suggester_state.select_previous();
                        } else {
                            app.auto_expert_switch_suggester_state.select_next();
                        }
                    } else {
                        // Tab cycling: Normal -> Logger -> Files -> Normal
                        app.input_mode = InputMode::LoggerFocus;
                        app.logger_state.enable_cursor_mode();
                    }
                }
                _ => {}
            }
        }
        InputMode::FilesFocus => {
            let sorted_file_paths: Vec<PathBuf> = app.ordered_files.get_paths_for_ui_display(); // Use OrderedFiles method
                                                                                                // sorted_file_paths.sort(); // Already sorted by get_paths_for_ui_display
            match key_event.code {
                KeyCode::Tab => {
                    // Tab cycling: Files -> Normal -> Logger -> Files
                    app.input_mode = InputMode::Normal;
                    app.focused_file_index = None;
                }
                KeyCode::Up | KeyCode::Char('k') => {
                    if !sorted_file_paths.is_empty() {
                        app.focused_file_index = match app.focused_file_index {
                            Some(idx) if idx > 0 => Some(idx - 1),
                            Some(_) => Some(sorted_file_paths.len() - 1), // Wrap to end
                            None => Some(sorted_file_paths.len() - 1),    // Start from end
                        };
                    }
                }
                KeyCode::Down | KeyCode::Char('j') => {
                    if !sorted_file_paths.is_empty() {
                        app.focused_file_index = match app.focused_file_index {
                            Some(idx) if idx < sorted_file_paths.len() - 1 => Some(idx + 1),
                            Some(_) => Some(0), // Wrap to start
                            None => Some(0),    // Start from beginning
                        };
                    }
                }
                KeyCode::Left | KeyCode::Char('h') => {
                    app.files_horizontal_scroll_offset -= 1;
                }
                KeyCode::Right | KeyCode::Char('l') => {
                    app.files_horizontal_scroll_offset += 1;
                }
                KeyCode::Char('d') if key_event.modifiers == KeyModifiers::NONE => {
                    // 'd' without modifiers
                    if let Some(idx) = app.focused_file_index {
                        if idx < sorted_file_paths.len() {
                            let path_to_remove = sorted_file_paths[idx].clone();
                            if app.ordered_files.remove_file(&path_to_remove) {
                                // Use OrderedFiles method
                                log::debug!(
                                    "\"{}\" has been dropped from context.",
                                    path_to_remove.display()
                                );
                                // Re-fetch sorted paths and adjust focus
                                let new_sorted_paths: Vec<PathBuf> =
                                    app.ordered_files.get_paths_for_ui_display(); // Use OrderedFiles
                                                                                  // new_sorted_paths.sort(); // Already sorted
                                if new_sorted_paths.is_empty() {
                                    app.focused_file_index = None;
                                    app.input_mode = InputMode::Normal; // Switch mode if no files left
                                } else {
                                    // Adjust focus to be within new bounds
                                    app.focused_file_index =
                                        Some(idx.min(new_sorted_paths.len() - 1));
                                }
                            }
                        }
                    }
                }
                _ => {}
            }
        }
        InputMode::LoggerFocus => {
            match key_event.code {
                KeyCode::Tab => {
                    // Tab cycling: Logger -> Files -> Normal -> Logger
                    app.logger_state.disable_cursor_mode();
                    app.input_mode = InputMode::FilesFocus;
                    if !app.ordered_files.is_empty() {
                        app.focused_file_index = Some(0);
                    } else {
                        app.focused_file_index = None;
                    }
                }
                // Vim-like navigation
                KeyCode::Char('h') | KeyCode::Left => {
                    // h/Left - move cursor left (not applicable for log view)
                }
                KeyCode::Char('j') | KeyCode::Down => {
                    // j/Down - move cursor down
                    app.logger_state.cursor_down();
                }
                KeyCode::Char('k') | KeyCode::Up => {
                    // k/Up - move cursor up
                    app.logger_state.cursor_up();
                }
                KeyCode::Char('l') | KeyCode::Right => {
                    // l/Right - move cursor right (not applicable for log view)
                }
                KeyCode::Char('g') => {
                    // gg - go to top (need to handle double g)
                    app.logger_state.cursor_to_top();
                }
                KeyCode::Char('G') => {
                    // G - go to bottom
                    app.logger_state.cursor_to_bottom();
                }
                KeyCode::PageUp => {
                    // Page up in cursor mode
                    app.logger_state.page_up();
                }
                KeyCode::PageDown => {
                    // Page down in cursor mode
                    app.logger_state.page_down();
                }
                KeyCode::Char('v') => {
                    // v - enter visual mode
                    if app.logger_state.is_visual_mode_enabled() {
                        app.logger_state.exit_visual_mode();
                    } else {
                        app.logger_state.enter_visual_mode();
                    }
                }
                KeyCode::Char('y') => {
                    // y - copy selection (yank) and exit visual mode
                    app.logger_state.copy_selection();
                }
                KeyCode::Esc => {
                    // Escape - exit visual mode or cursor mode
                    if app.logger_state.is_visual_mode_enabled() {
                        app.logger_state.exit_visual_mode();
                    } else {
                        app.logger_state.disable_cursor_mode();
                        app.input_mode = InputMode::Normal;
                    }
                }
                _ => {}
            }
        }
        InputMode::AwaitingRunConfirmation => {
            match key_event.code {
                KeyCode::Enter => {
                    let mut user_response = app.input.trim().to_lowercase();
                    if user_response.is_empty() {
                        // Default to Yes
                        user_response = "y".to_string();
                    }

                    if user_response == "y" || user_response == "yes" {
                        if let Some(output_to_append) = app.last_run_command_output.take() {
                            let command_executed = app
                                .last_run_command_executed
                                .take()
                                .unwrap_or_else(|| "<unknown command>".to_string());
                            let formatted_output = format!(
                                "--- Command executed in directory '{}': ---\n{}\n--- Output of command: ---\n{}\n--- End of command output ---",
                                app.current_path.display(),
                                command_executed,
                                output_to_append
                            );
                            match app.app_config.task_info.as_mut() {
                                Some(existing_info) if !existing_info.is_empty() => {
                                    existing_info.push_str("\n\n"); // Add some spacing
                                    existing_info.push_str(&formatted_output);
                                }
                                _ => {
                                    // Task info was None or empty
                                    app.app_config.task_info = Some(formatted_output.clone());
                                }
                            }
                            log::debug!("Command and output appended to task info.");
                        }
                    } else if user_response == "n" || user_response == "no" {
                        // Explicitly check for "no"
                        app.last_run_command_output.take(); // Discard
                        app.last_run_command_executed.take(); // Discard
                        log::debug!("Command output will not be added to task info.");
                    } else {
                        // Invalid input, keep AwaitingRunConfirmation mode and prompt again (implicitly by not changing mode)
                        log::debug!("Invalid input. Do you want to add this output to task info? (Y)es/(N)o [Yes]");
                        // Do not clear input here, let user correct it or re-enter
                        return; // Return early to prevent mode change and input clear
                    }
                    // If 'y' or 'n' was processed:
                    app.input_mode = InputMode::Normal;
                    app.input.clear();
                    app.input_cursor_char_idx = 0;
                }
                KeyCode::Char(c) => {
                    // Allow typing 'y', 'n', 'yes', 'no'
                    let byte_idx = char_idx_to_byte_idx(&app.input, app.input_cursor_char_idx);
                    app.input.insert(byte_idx, c);
                    app.input_cursor_char_idx += 1;
                }
                KeyCode::Backspace => {
                    if app.input_cursor_char_idx > 0 {
                        let char_idx_to_remove = app.input_cursor_char_idx - 1;
                        let byte_idx = char_idx_to_byte_idx(&app.input, char_idx_to_remove);
                        if byte_idx < app.input.len() {
                            app.input.remove(byte_idx);
                            app.input_cursor_char_idx -= 1;
                        }
                    }
                }
                KeyCode::Left => {
                    if app.input_cursor_char_idx > 0 {
                        app.input_cursor_char_idx -= 1;
                    }
                }
                KeyCode::Right => {
                    if app.input_cursor_char_idx < app.input.chars().count() {
                        app.input_cursor_char_idx += 1;
                    }
                }
                _ => {}
            }
        }
    }
}
