use directories::UserDirs;
use ratatui::{
    layout::{Alignment, Constraint, Direction, Layout, Rect},
    style::{Color, Style, Stylize}, // Stylize added back
    text::{Line, Span, Text},       // Added Line, Span, Text
    widgets::{Block, Borders, Gauge, Paragraph},
    Frame,
};
use std::path::{Path, PathBuf}; // Added Path, PathBuf
use crate::tui::tui_logger::TuiLoggerWidget;

use super::app::{EditPlanItemStatus, InputMode, InteractiveApp}; // Added EditPlanItemStatus

// Helper function to generate the display path and filename start index
fn generate_smart_path_display(
    absolute_path: &Path,
    current_dir: &Path,
    home_dir_option: Option<&Path>,
) -> (String, usize) {
    // Returns (display_string, filename_char_start_index)
    let display_path_string: String;

    // Attempt to make relative to current_dir
    if let Ok(rel_path) = absolute_path.strip_prefix(current_dir) {
        let mut temp_rel_path_str = rel_path.to_string_lossy().into_owned();
        // Prepend "./" if it's not already there and not an absolute-like path from root of current_dir
        if rel_path
            .components()
            .next()
            .map_or(true, |c| c.as_os_str() != "." && c.as_os_str() != "..")
            && !rel_path.is_absolute()
        {
            temp_rel_path_str = format!(".{}{}", std::path::MAIN_SEPARATOR, temp_rel_path_str);
        }
        display_path_string = temp_rel_path_str;
    } else if let Some(home_dir) = home_dir_option {
        if let Ok(rel_to_home_path) = absolute_path.strip_prefix(home_dir) {
            display_path_string = format!(
                "~{}{}",
                std::path::MAIN_SEPARATOR,
                rel_to_home_path.to_string_lossy()
            );
        } else {
            // Not under home, use absolute path
            display_path_string = absolute_path.to_string_lossy().into_owned();
        }
    } else {
        // Fallback to absolute path if no home_dir or not under home
        display_path_string = absolute_path.to_string_lossy().into_owned();
    }

    let filename_os_str = absolute_path
        .file_name()
        .unwrap_or_else(|| std::ffi::OsStr::new(""));
    let filename_str = filename_os_str.to_string_lossy();

    let filename_char_start_index = if filename_str.is_empty() {
        display_path_string.chars().count() // Filename is empty, "starts" at the end
    } else {
        // Find the last occurrence of the filename in the display_path_string
        if let Some(byte_offset) = display_path_string.rfind(&*filename_str) {
            display_path_string[..byte_offset].chars().count()
        } else {
            // Fallback: if filename string not found (highly unlikely for valid paths),
            // assume it's after the last separator or specific prefixes.
            if let Some(sep_byte_idx) = display_path_string.rfind(std::path::MAIN_SEPARATOR) {
                display_path_string[..sep_byte_idx].chars().count() + 1
            } else if (display_path_string.starts_with("./")
                || display_path_string.starts_with("~/"))
                && display_path_string.len() > 2
            {
                2 // After "./" or "~/"
            } else {
                0 // Filename starts at the beginning (e.g. "file.txt")
            }
        }
    };
    (display_path_string, filename_char_start_index)
}

fn draw_lledit_log_view(frame: &mut Frame, app: &mut InteractiveApp, area: Rect) {
    let (display_model_name, title_prefix_str) = if app.is_processing()
        || app.is_auto_researching
        || app.is_researching
        || app.is_asking_question
    {
        // An operation is active, use current_operation_model_alias
        (
            app.current_operation_model_alias
                .as_deref()
                .unwrap_or(&app.app_config.default_model) // Fallback
                .to_string(),
            "Model: ",
        )
    } else {
        // No operation active, display the default model
        (app.app_config.default_model.clone(), "Default Model: ")
    };

    let mut capitalized_alias_chars = display_model_name.chars();
    let cap_alias = capitalized_alias_chars
        .next()
        .map_or_else(String::new, |c| {
            c.to_uppercase().to_string() + capitalized_alias_chars.as_str()
        });
    let model_title_part = format!("<{}{}> ", title_prefix_str, cap_alias);

    let (mode_specific_title_part, style) = if app.is_auto_researching {
        (
            format!(
                "(Auto-Researching) {}[Commands Issued: {}] ",
                model_title_part, app.research_bash_commands_issued
            ),
            Style::default().fg(Color::Magenta),
        )
    } else if app.is_processing {
        (
            format!("(Editing) {}", model_title_part),
            Style::default().fg(Color::Yellow),
        )
    } else if app.is_researching {
        (
            format!(
                "(Researching) {}[Commands Issued: {}] ",
                model_title_part, app.research_bash_commands_issued
            ),
            Style::default().fg(Color::Magenta),
        )
    } else if app.is_asking_question {
        (
            format!("(Question) {}", model_title_part),
            Style::default().fg(Color::Cyan),
        )
    } else {
        // Inactive state - model_title_part already contains "Default Model: ..."
        (
            format!("- {}", model_title_part), // model_title_part now includes "Default Model: "
            Style::default().fg(Color::LightBlue),
        )
    };

    let base_title = format!(" LLEdit {} ", mode_specific_title_part);

    let retry_title_part =
        if app.task_retry_count > 0 && (app.is_processing || app.is_auto_researching) {
            format!("<Task Retries: {}> ", app.task_retry_count)
        } else {
            "".to_string()
        };

    let full_title = format!("{}{}", base_title, retry_title_part)
        .trim_end()
        .to_string(); // Trim trailing space if retry_title_part is empty

    let log_widget = TuiLoggerWidget::new()
        .block(
            Block::default()
                .title(format!("{} ", full_title)) // Add a space at the end for padding inside border
                .borders(Borders::ALL)
                .style(style),
        );
    frame.render_stateful_widget(log_widget, area, &mut app.logger_state);
}

fn draw_status_box(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    if app.is_processing
        || app.is_researching
        || app.is_asking_question
        || app.is_auto_researching
        || app.show_success_state_until.is_some()
    {
        let (progress_value, border_color, title_text, label_text) = if app.is_auto_researching {
            (
                1.0,
                Color::Magenta,
                " Auto-Research Status ",
                "Researching...".to_string(),
            )
        } else if app.is_asking_question {
            (
                1.0,
                Color::Cyan,
                " Question Status ",
                "Asking...".to_string(),
            )
        } else if app.is_processing
            || (app.show_success_state_until.is_some()
                && !app.is_researching
                && !app.is_asking_question
                && !app.is_auto_researching)
        {
            // Edit processing or showing success for edit (and not in other active states)
            let progress = if app.show_success_state_until.is_some() && !app.is_processing {
                1.0
            } else {
                app.processing_progress
            };
            let percentage = (progress * 100.0).clamp(0.0, 100.0) as u16;
            (
                progress,
                Color::Green,
                " Edit Status ",
                format!("{}%", percentage),
            )
        } else {
            // Must be app.is_researching (and not asking question, auto-researching, or showing edit success)
            (
                1.0,
                Color::Magenta,
                " Research Status ",
                "Researching...".to_string(),
            )
        };

        let progress_percentage_for_gauge = (progress_value * 100.0).clamp(0.0, 100.0) as u16;

        let gauge = Gauge::default()
            .block(
                Block::default()
                    .title(title_text)
                    .borders(Borders::ALL)
                    .style(Style::default().fg(border_color)),
            )
            .gauge_style(Style::default().fg(border_color).bg(Color::DarkGray))
            .percent(progress_percentage_for_gauge)
            .label(label_text);
        frame.render_widget(gauge, area);
    } else {
        // Inactive state
        let vertical_center_chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints(
                [
                    Constraint::Min(0),    // Top padding
                    Constraint::Length(1), // Text area (1 line high)
                    Constraint::Min(0),    // Bottom padding
                ]
                .as_ref(),
            )
            .split(area); // Use the passed 'area'

        let text_display_area = if area.height >= 1 {
            // Ensure area has height
            vertical_center_chunks[1]
        } else {
            area // Fallback if area is too small
        };

        let status_block = Block::default()
            .title(" Status ")
            .borders(Borders::ALL)
            .style(Style::default().fg(Color::LightBlue));
        frame.render_widget(status_block, area);

        let inactive_text_paragraph = Paragraph::new("Inactive")
            .style(Style::default().fg(Color::LightBlue))
            .alignment(Alignment::Center);
        frame.render_widget(inactive_text_paragraph, text_display_area);
    }
}

fn draw_files_box(
    frame: &mut Frame,
    app: &mut InteractiveApp,
    area: Rect,
    home_dir_option: Option<&PathBuf>,
) {
    // app is mutable, home_dir_option added
    let sorted_file_paths: Vec<_> = app.ordered_files.get_paths_for_ui_display(); // Use OrderedFiles method
                                                                                  // sorted_file_paths.sort(); // Already sorted by get_paths_for_ui_display()

    let list_len = sorted_file_paths.len();
    let max_visible_files = area.height.saturating_sub(2) as usize;

    app.adjust_files_scroll(list_len, max_visible_files);

    let mut lines: Vec<Line> = Vec::new();
    let files_box_content_width = area.width.saturating_sub(2).max(1) as usize; // Ensure at least 1 for content width calculation

    let visible_files_to_render: Vec<_> = sorted_file_paths
        .iter()
        .enumerate()
        .skip(app.files_scroll_offset)
        .take(max_visible_files)
        .collect();

    for (original_idx, absolute_path) in visible_files_to_render {
        let (smart_path_str, filename_start_char_idx) = generate_smart_path_display(
            absolute_path,
            &app.current_path,
            home_dir_option.as_deref().map(|v| &**v),
        );

        // Calculate how many characters to skip from the start of smart_path_str
        // app.files_horizontal_scroll_offset: positive shifts view right (content left), negative shifts view left (content right)
        let chars_to_skip_from_smart_path_start =
            (filename_start_char_idx as isize + app.files_horizontal_scroll_offset).max(0) as usize;

        let display_str_segment: String = smart_path_str
            .chars()
            .skip(chars_to_skip_from_smart_path_start)
            .take(files_box_content_width)
            .collect();

        let style = if app.input_mode == InputMode::FilesFocus
            && !app.is_processing
            && !app.is_auto_researching
            && !app.is_researching
            && !app.is_asking_question
            && app.focused_file_index == Some(original_idx)
        {
            Style::default().fg(Color::Yellow).bold()
        } else {
            Style::default().fg(Color::White)
        };
        lines.push(Line::from(Span::styled(display_str_segment, style)));
    }

    let files_text = Text::from(lines);

    let (files_title, files_border_style) = if app.is_auto_researching
        || app.is_processing
        || app.is_researching
        || app.is_asking_question
    {
        (" Files ", Style::default().fg(Color::DarkGray)) // Files view is less active during any operation
    } else if app.input_mode == InputMode::FilesFocus {
        (" Files (d Drop) ", Style::default().fg(Color::Yellow))
    } else {
        (
            " Files (Tab to focus) ",
            Style::default().fg(Color::LightBlue),
        )
    };

    let files_paragraph = Paragraph::new(files_text).block(
        Block::default()
            .title(files_title)
            .borders(Borders::ALL)
            .style(files_border_style),
    );
    frame.render_widget(files_paragraph, area);
}

fn draw_input_box(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    let (input_title, input_border_style, input_text_style) = if app.is_auto_researching {
        (
            " Input ",
            Style::default().fg(Color::DarkGray),
            Style::default().fg(Color::DarkGray),
        )
    } else if app.is_processing {
        (
            " Input ",
            Style::default().fg(Color::DarkGray),
            Style::default().fg(Color::DarkGray),
        )
    } else if app.is_researching {
        (
            " Input ",
            Style::default().fg(Color::DarkGray),
            Style::default().fg(Color::DarkGray),
        )
    } else if app.is_asking_question {
        (
            " Input ",
            Style::default().fg(Color::DarkGray),
            Style::default().fg(Color::DarkGray),
        )
    } else if app.input_mode == InputMode::Normal {
        (
            " Input (Enter submit, C-q quit) ",
            Style::default().fg(Color::Yellow),
            Style::default().fg(Color::Yellow),
        )
    } else if app.input_mode == InputMode::AwaitingRunConfirmation {
        (
            " Confirm (Y)es/(N)o [Yes]: ",           // Updated title
            Style::default().fg(Color::Cyan).bold(), // Highlight for confirmation
            Style::default().fg(Color::Cyan),
        )
    } else {
        // FilesFocus mode
        (
            " Input (Tab to focus input) ",
            Style::default().fg(Color::LightBlue),
            Style::default().fg(Color::LightBlue),
        )
    };

    let mut display_input_text = app.input.as_str();
    let mut scroll_offset_chars: usize = 0;

    if !app.is_auto_researching
        && !app.is_processing
        && !app.is_researching
        && !app.is_asking_question
    {
        // Only apply scrolling if not in any active operation
        let content_width = area.width.saturating_sub(2).max(1) as usize;
        let cursor_char_idx = app.input_cursor_char_idx;
        let input_len = app.input.chars().count();

        if cursor_char_idx >= scroll_offset_chars + content_width {
            scroll_offset_chars = cursor_char_idx - content_width + 1;
        } else if cursor_char_idx < scroll_offset_chars {
            scroll_offset_chars = cursor_char_idx;
        }
        // Ensure scroll_offset_chars doesn't go too far if input is shorter than width or empty
        if input_len < content_width {
            scroll_offset_chars = 0;
        } else {
            scroll_offset_chars = scroll_offset_chars.min(input_len.saturating_sub(content_width));
        }

        // Get the slice of the string to display
        // Iterate by chars to handle UTF-8 correctly for slicing by character index
        let start_byte_idx = app
            .input
            .char_indices()
            .nth(scroll_offset_chars)
            .map_or(0, |(idx, _)| idx);
        let end_byte_idx = app
            .input
            .char_indices()
            .nth(scroll_offset_chars + content_width)
            .map_or(app.input.len(), |(idx, _)| idx);
        display_input_text = &app.input[start_byte_idx..end_byte_idx];
    }
    let paragraph_content = if app.is_auto_researching {
        let spinner_char = app.spinner_chars[app.spinner_index % app.spinner_chars.len()];
        format!(
            "Auto-Researching... Please wait. (Ctrl+C to Stop) {}",
            spinner_char
        )
    } else if app.is_processing {
        let spinner_char = app.spinner_chars[app.spinner_index % app.spinner_chars.len()];
        format!("Editing... Please wait. (Ctrl+C to Stop) {}", spinner_char)
    } else if app.is_researching {
        let spinner_char = app.spinner_chars[app.spinner_index % app.spinner_chars.len()];
        format!(
            "Researching... Please wait. (Ctrl+C to Stop) {}",
            spinner_char
        )
    } else if app.is_asking_question {
        let spinner_char = app.spinner_chars[app.spinner_index % app.spinner_chars.len()];
        format!(
            "Asking question... Please wait. (Ctrl+C to Stop) {}",
            spinner_char
        )
    } else {
        display_input_text.to_string()
    };

    let input_paragraph_widget = Paragraph::new(paragraph_content).style(input_text_style);

    let input_block = Block::default()
        .borders(Borders::ALL)
        .title(input_title)
        .style(input_border_style);

    let final_input_widget = input_paragraph_widget.block(input_block);
    frame.render_widget(final_input_widget, area);

    // Show cursor only if not in any active operation AND (in Normal mode OR AwaitingRunConfirmation mode)
    if !app.is_auto_researching
        && !app.is_processing
        && !app.is_researching
        && !app.is_asking_question
        && (app.input_mode == InputMode::Normal
            || app.input_mode == InputMode::AwaitingRunConfirmation)
    {
        let cursor_display_pos_chars = app
            .input_cursor_char_idx
            .saturating_sub(scroll_offset_chars);

        frame.set_cursor_position((area.x + 1 + cursor_display_pos_chars as u16, area.y + 1));
    }
}

pub fn draw_ui(frame: &mut Frame, app: &mut InteractiveApp) {
    // app is now mutable
    let mut top_constraints = vec![Constraint::Min(0)];
    let mut current_popup_height = 0;
    let mut is_any_popup_active = false;

    if app.autocomplete_state.active {
        current_popup_height = app.autocomplete_state.get_popup_height() as u16;
        is_any_popup_active = true;
    } else if app.file_suggester_state.active {
        current_popup_height = app.file_suggester_state.get_popup_height() as u16;
        is_any_popup_active = true;
    } else if app.model_suggester_state.active {
        current_popup_height = app.model_suggester_state.get_popup_height() as u16;
        is_any_popup_active = true;
    } else if app.auto_research_mode_suggester_state.active {
        // Added
        current_popup_height = app.auto_research_mode_suggester_state.get_popup_height() as u16;
        is_any_popup_active = true;
    } else if app.auto_expert_switch_suggester_state.active {
        // Added
        current_popup_height = app.auto_expert_switch_suggester_state.get_popup_height() as u16;
        is_any_popup_active = true;
    }

    if is_any_popup_active && current_popup_height > 0 {
        top_constraints.push(Constraint::Length(current_popup_height));
    } else {
        // If a popup is active but height is 0 (e.g. "Searching..." message for file suggester)
        // we still need to allocate space for its 1-line text + borders.
        // The get_popup_height() in file_suggester_state already accounts for this.
        // So, if current_popup_height is 0 here, it means no popup should be drawn or take space.
        current_popup_height = 0;
    }
    top_constraints.push(Constraint::Length(3)); // Input box

    let main_vertical_chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints(top_constraints)
        .split(frame.area());

    let log_area_index = 0;
    let mut current_idx_for_chunks = log_area_index;

    let suggestion_popup_area = if current_popup_height > 0 {
        current_idx_for_chunks += 1;
        Some(main_vertical_chunks[current_idx_for_chunks])
    } else {
        None
    };

    current_idx_for_chunks += 1; // Corrected variable name
    let input_area = main_vertical_chunks[current_idx_for_chunks]; // Corrected variable name
    let top_log_and_sidebar_area = main_vertical_chunks[log_area_index];

    // Fetch home directory once for use in draw_files_box
    let home_dir = UserDirs::new().map(|ud| ud.home_dir().to_path_buf());

    let top_horizontal_chunks = Layout::default()
        .direction(Direction::Horizontal)
        .constraints([
            Constraint::Percentage(80), // LLEdit/Log view
            Constraint::Percentage(20), // Right sidebar
        ])
        .split(top_log_and_sidebar_area); // This area is now correctly sized

    let lledit_log_area = top_horizontal_chunks[0];
    let right_column_area = top_horizontal_chunks[1];

    let editing_plan_visible = (app.is_processing || app.show_success_state_until.is_some())
        && !app.editing_plan_items.is_empty();
    let mut right_column_constraints = vec![Constraint::Length(3)]; // Status box

    if editing_plan_visible {
        // Calculate height for editing plan: items + borders/title, capped at 12 lines total.
        let plan_items_count = app.editing_plan_items.len();
        let calculated_plan_height = (plan_items_count + 2).min(12) as u16; // Max 10 items + 2 for borders
        right_column_constraints.push(Constraint::Length(calculated_plan_height));
    }
    right_column_constraints.push(Constraint::Min(0)); // Files box takes remaining space

    let right_column_chunks = Layout::default()
        .direction(Direction::Vertical)
        .constraints(right_column_constraints)
        .split(right_column_area);

    let status_area = right_column_chunks[0];
    let mut current_chunk_index = 1;

    draw_lledit_log_view(frame, app, lledit_log_area);
    draw_status_box(frame, app, status_area);

    if editing_plan_visible {
        let editing_plan_area = right_column_chunks[current_chunk_index];
        draw_editing_plan_box(frame, app, editing_plan_area);
        current_chunk_index += 1;
    }

    let files_area = right_column_chunks[current_chunk_index];
    draw_files_box(frame, app, files_area, home_dir.as_ref()); // Pass home_dir Option
    draw_input_box(frame, app, input_area);

    if let Some(popup_rect) = suggestion_popup_area {
        // Check which popup is active and draw it.
        // The height calculation ensures only one can have height > 0 at a time.
        if app.autocomplete_state.active {
            draw_command_autocomplete_popup(frame, app, popup_rect);
        } else if app.file_suggester_state.active {
            draw_file_suggestion_popup(frame, app, popup_rect);
        } else if app.model_suggester_state.active {
            draw_model_suggestion_popup(frame, app, popup_rect);
        } else if app.auto_research_mode_suggester_state.active {
            // Added
            draw_auto_research_mode_suggestion_popup(frame, app, popup_rect);
        } else if app.auto_expert_switch_suggester_state.active {
            // Added
            draw_auto_expert_switch_suggestion_popup(frame, app, popup_rect);
        }
    }
}

fn draw_command_autocomplete_popup(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    if !app.autocomplete_state.active || area.height == 0 {
        return;
    }

    let suggestions = app.autocomplete_state.get_visible_suggestions();
    if suggestions.is_empty() {
        return;
    }

    let mut lines: Vec<Line> = Vec::new();
    for (idx_in_visible, suggestion) in suggestions.iter().enumerate() {
        // Calculate actual index in filtered_suggestions
        let actual_idx = app.autocomplete_state.scroll_offset + idx_in_visible;

        let style = if Some(actual_idx) == app.autocomplete_state.selected_index {
            Style::default().fg(Color::Black).bg(Color::Cyan)
        } else {
            Style::default().fg(Color::Cyan)
        };
        // Display suggestion display_text - description, potentially truncated
        let full_suggestion_text =
            format!("{} - {}", suggestion.display_text, suggestion.description);
        let available_width = area.width.saturating_sub(4); // 2 for borders, 2 for padding

        let display_text_for_popup = if full_suggestion_text.len() > available_width as usize {
            format!(
                "{}...",
                &full_suggestion_text[..available_width.saturating_sub(3) as usize]
            )
        } else {
            full_suggestion_text
        };
        lines.push(Line::from(Span::styled(display_text_for_popup, style)));
    }

    let paragraph = Paragraph::new(lines)
        .block(
            Block::default()
                .title(" Suggestions ")
                .borders(Borders::ALL)
                .style(Style::default().fg(Color::Cyan)),
        )
        .alignment(Alignment::Left);

    frame.render_widget(paragraph, area);
}

fn draw_model_suggestion_popup(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    if !app.model_suggester_state.active || area.height == 0 {
        return;
    }

    let suggestions = app.model_suggester_state.get_visible_suggestions();

    if suggestions.is_empty() {
        let mut message_text = "No matches found.";
        if app.model_suggester_state.current_search_term.is_empty()
            && app.model_suggester_state.all_models.is_empty()
        {
            message_text = "No models configured in models_list.";
        } else if app.model_suggester_state.current_search_term.is_empty()
            && !app.model_suggester_state.all_models.is_empty()
        {
            // If search term is empty but there are models, don't show "No matches", show the list or nothing if popup height is 0.
            // This case is handled by suggestions not being empty if all_models is not empty and search term is empty.
            // So, if suggestions is empty here, it means either no models or no matches for a non-empty term.
        }

        let no_matches_paragraph = Paragraph::new(Span::styled(
            message_text,
            Style::default().fg(Color::DarkGray),
        ))
        .block(
            Block::default()
                .title(" Models List ")
                .borders(Borders::ALL)
                .style(Style::default().fg(Color::DarkGray)),
        )
        .alignment(Alignment::Center);
        frame.render_widget(no_matches_paragraph, area);
        return;
    }

    let mut lines: Vec<Line> = Vec::new();
    for (idx_in_visible, suggestion) in suggestions.iter().enumerate() {
        let actual_idx = app.model_suggester_state.scroll_offset + idx_in_visible;
        let style = if Some(actual_idx) == app.model_suggester_state.selected_index {
            Style::default().fg(Color::Black).bg(Color::LightYellow) // Different highlight
        } else {
            Style::default().fg(Color::LightYellow)
        };

        let available_width = area.width.saturating_sub(4); // borders + padding
        let display_text = if suggestion.display_text.len() > available_width as usize {
            format!(
                "{}...",
                &suggestion.display_text[..available_width.saturating_sub(3) as usize]
            )
        } else {
            suggestion.display_text.clone()
        };
        lines.push(Line::from(Span::styled(display_text, style)));
    }

    let paragraph = Paragraph::new(lines)
        .block(
            Block::default()
                .title(" Models List ")
                .borders(Borders::ALL)
                .style(Style::default().fg(Color::LightYellow)),
        )
        .alignment(Alignment::Left);

    frame.render_widget(paragraph, area);
}

fn draw_editing_plan_box(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    let block_title = " Editing Plan ";
    let border_style = if app.is_processing {
        Style::default().fg(Color::Yellow)
    } else if app.show_success_state_until.is_some() {
        Style::default().fg(Color::Green) // Show green border when success state is active
    } else {
        Style::default().fg(Color::DarkGray) // Should not happen if visibility condition is correct
    };

    let plan_block = Block::default()
        .title(block_title)
        .borders(Borders::ALL)
        .style(border_style);

    let list_items: Vec<Line> = app
        .editing_plan_items
        .iter()
        .enumerate()
        .map(|(idx, item)| {
            let (status_icon, spacing) = match item.status {
                EditPlanItemStatus::Pending => ("  ", ""), // Two spaces for icon, no extra space after icon needed
                EditPlanItemStatus::Processing => ("⏳", " "), // Icon, one space
                EditPlanItemStatus::Success => ("✅", " "), // Icon, one space
                EditPlanItemStatus::Failure => ("❎", " "), // Icon, one space
            };
            let range_text = if item.start_line == 0 && item.end_line == 0 {
                "New File".to_string()
            } else {
                format!("{}-{}", item.start_line, item.end_line)
            };
            let text = format!(
                "{}{}{} [{}]",
                status_icon, spacing, range_text, item.filename
            );

            let style = if Some(idx) == app.current_processing_target_index
                && item.status == EditPlanItemStatus::Processing
            {
                Style::default().fg(Color::Yellow).bold()
            } else if item.status == EditPlanItemStatus::Success {
                Style::default().fg(Color::Green)
            } else if item.status == EditPlanItemStatus::Failure {
                Style::default().fg(Color::Red)
            } else {
                Style::default().fg(Color::White)
            };
            Line::from(Span::styled(text, style))
        })
        .collect();

    // Basic scrolling logic (can be enhanced later if needed)
    let max_visible_items = area.height.saturating_sub(2) as usize; // -2 for borders
    let scroll_offset = if let Some(current_idx) = app.current_processing_target_index {
        if current_idx >= max_visible_items {
            current_idx.saturating_sub(max_visible_items / 2) // Try to keep current item somewhat centered
        } else {
            0
        }
    } else {
        0 // Default to top if no item is actively processing
    };

    let visible_items: Vec<Line> = list_items
        .into_iter()
        .skip(scroll_offset)
        .take(max_visible_items)
        .collect();

    let paragraph = Paragraph::new(visible_items).block(plan_block);
    frame.render_widget(paragraph, area);
}

// The duplicated draw_file_suggestion_popup function is removed by this block.
// The correct one (identical to the one above in the SEARCH section) should remain.

fn draw_auto_research_mode_suggestion_popup(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    if !app.auto_research_mode_suggester_state.active || area.height == 0 {
        return;
    }

    let suggestions = app
        .auto_research_mode_suggester_state
        .get_visible_suggestions();
    let mut lines: Vec<Line> = Vec::new();

    if suggestions.is_empty()
        && !app
            .auto_research_mode_suggester_state
            .current_search_term
            .is_empty()
    {
        lines.push(Line::from(Span::styled(
            "No matches found.",
            Style::default().fg(Color::DarkGray),
        )));
    } else {
        for (idx_in_visible, suggestion) in suggestions.iter().enumerate() {
            let actual_idx = app.auto_research_mode_suggester_state.scroll_offset + idx_in_visible;
            let style = if Some(actual_idx) == app.auto_research_mode_suggester_state.selected_index
            {
                Style::default().fg(Color::Black).bg(Color::LightMagenta)
            } else {
                Style::default().fg(Color::LightMagenta)
            };
            let full_suggestion_text = format!("{} - {}", suggestion.name, suggestion.description);
            let available_width = area.width.saturating_sub(4);
            let display_text = if full_suggestion_text.len() > available_width as usize {
                format!(
                    "{}...",
                    &full_suggestion_text[..available_width.saturating_sub(3) as usize]
                )
            } else {
                full_suggestion_text
            };
            lines.push(Line::from(Span::styled(display_text, style)));
        }
    }

    let title = if suggestions.is_empty()
        && app
            .auto_research_mode_suggester_state
            .current_search_term
            .is_empty()
    {
        " Auto-Research Modes (Type to filter) "
    } else {
        " Auto-Research Mode Suggestions "
    };

    let paragraph = Paragraph::new(lines)
        .block(
            Block::default()
                .title(title)
                .borders(Borders::ALL)
                .style(Style::default().fg(Color::LightMagenta)),
        )
        .alignment(Alignment::Left);
    frame.render_widget(paragraph, area);
}

fn draw_auto_expert_switch_suggestion_popup(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    if !app.auto_expert_switch_suggester_state.active || area.height == 0 {
        return;
    }

    let suggestions = app
        .auto_expert_switch_suggester_state
        .get_visible_suggestions();
    let mut lines: Vec<Line> = Vec::new();

    if suggestions.is_empty()
        && !app
            .auto_expert_switch_suggester_state
            .current_search_term
            .is_empty()
    {
        lines.push(Line::from(Span::styled(
            "No matches found.",
            Style::default().fg(Color::DarkGray),
        )));
    } else {
        for (idx_in_visible, suggestion) in suggestions.iter().enumerate() {
            let actual_idx = app.auto_expert_switch_suggester_state.scroll_offset + idx_in_visible;
            let style = if Some(actual_idx) == app.auto_expert_switch_suggester_state.selected_index
            {
                Style::default().fg(Color::Black).bg(Color::LightCyan)
            } else {
                Style::default().fg(Color::LightCyan)
            };
            let full_suggestion_text = format!("{} - {}", suggestion.name, suggestion.description);
            let available_width = area.width.saturating_sub(4);
            let display_text = if full_suggestion_text.len() > available_width as usize {
                format!(
                    "{}...",
                    &full_suggestion_text[..available_width.saturating_sub(3) as usize]
                )
            } else {
                full_suggestion_text
            };
            lines.push(Line::from(Span::styled(display_text, style)));
        }
    }

    let title = if suggestions.is_empty()
        && app
            .auto_expert_switch_suggester_state
            .current_search_term
            .is_empty()
    {
        " Auto-Expert Modes (Type to filter) "
    } else {
        " Auto-Expert Mode Suggestions "
    };

    let paragraph = Paragraph::new(lines)
        .block(
            Block::default()
                .title(title)
                .borders(Borders::ALL)
                .style(Style::default().fg(Color::LightCyan)),
        )
        .alignment(Alignment::Left);
    frame.render_widget(paragraph, area);
}

fn draw_file_suggestion_popup(frame: &mut Frame, app: &InteractiveApp, area: Rect) {
    if !app.file_suggester_state.active || area.height == 0 {
        return;
    }

    let suggestions = app.file_suggester_state.get_visible_suggestions();
    if suggestions.is_empty() {
        // Optionally, display a "No matches" message if search_term is not empty
        if !app.file_suggester_state.search_term.is_empty() {
            let no_matches_text = Paragraph::new(Span::styled(
                "No matches found.",
                Style::default().fg(Color::DarkGray),
            ))
            .block(
                Block::default()
                    .title(" File Suggestions ")
                    .borders(Borders::ALL)
                    .style(Style::default().fg(Color::DarkGray)),
            )
            .alignment(Alignment::Center);
            frame.render_widget(no_matches_text, area);
        } // If search term is empty (listing dir), and suggestions empty, draw nothing or an empty box.
        return;
    }

    let mut lines: Vec<Line> = Vec::new();
    for (idx_in_visible, suggestion) in suggestions.iter().enumerate() {
        let actual_idx = app.file_suggester_state.scroll_offset + idx_in_visible;
        let style = if Some(actual_idx) == app.file_suggester_state.selected_index {
            Style::default().fg(Color::Black).bg(Color::LightGreen) // Different highlight for files
        } else {
            Style::default().fg(Color::LightGreen)
        };

        let available_width = area.width.saturating_sub(4); // borders + padding
        let display_name = if suggestion.display_name.len() > available_width as usize {
            format!(
                "{}...",
                &suggestion.display_name[..available_width.saturating_sub(3) as usize]
            )
        } else {
            suggestion.display_name.clone()
        };
        lines.push(Line::from(Span::styled(display_name, style)));
    }

    let paragraph = Paragraph::new(lines)
        .block(
            Block::default()
                .title(" File Suggestions ")
                .borders(Borders::ALL)
                .style(Style::default().fg(Color::LightGreen)),
        )
        .alignment(Alignment::Left);

    frame.render_widget(paragraph, area);
}
