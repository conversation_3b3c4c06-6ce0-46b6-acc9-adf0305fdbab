//! # Custom TUI Logger Implementation
//!
//! This module provides a complete replacement for the `tui-logger` crate with enhanced features:
//! - Advanced text wrapping and rendering
//! - Cursor navigation and visual selection
//! - Typewriter effects for animated text
//! - Custom styling and log levels
//! - Seamless integration with the `log` crate
//!
//! ## Usage
//!
//! Initialize the logger in your main application:
//! ```rust
//! let log_receiver = crate::tui::tui_logger::init();
//! ```
//!
//! Use standard logging macros throughout your application:
//! ```rust
//! log::info!("This will appear in the TUI");
//! log::error!("Error messages are styled in red");
//! ```
//!
//! Or use the enhanced macros for special effects:
//! ```rust
//! tui_typewriter_info!(100, "This text will appear with typewriter effect");
//! ```

use chrono::{DateTime, Local, Timelike};
use log::{Level, Log, Metadata, Record};
use once_cell::sync::OnceCell;
use ratatui::style::{Color, Style};
use ratatui::text::{Line, Span, Text};
use ratatui::widgets::{Block, Paragraph, StatefulWidget, Widget, Wrap};
use ratatui::{buffer::Buffer, layout::Rect};
use std::collections::VecDeque;
use std::time::Instant;
use tokio::sync::mpsc;

use crate::interactive::commands_utils::copy_to_clipboard;

/// Represents the log level for a message with support for custom levels
#[derive(Clone, Debug)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
    Custom(String), // For special-purpose logs
}

impl From<Level> for LogLevel {
    fn from(level: Level) -> Self {
        match level {
            Level::Trace => LogLevel::Trace,
            Level::Debug => LogLevel::Debug,
            Level::Info => LogLevel::Info,
            Level::Warn => LogLevel::Warn,
            Level::Error => LogLevel::Error,
        }
    }
}

/// Configuration for typewriter effect animation
///
/// Controls how text is revealed character by character for dramatic effect
#[derive(Clone, Debug)]
pub struct TypewriterEffect {
    pub enabled: bool,
    pub speed_millis: u64, // Milliseconds per character
}

impl Default for TypewriterEffect {
    fn default() -> Self {
        Self {
            enabled: false,
            speed_millis: 50,
        }
    }
}

/// A single log message with styling and effects
#[derive(Clone, Debug)]
pub struct LogMessage {
    pub timestamp: DateTime<Local>,
    pub level: LogLevel,
    pub message: String,

    // Styling overrides for custom logs
    pub style: Option<Style>,
    pub typewriter: Option<TypewriterEffect>,
}

impl LogMessage {
    pub fn new(level: LogLevel, message: String) -> Self {
        Self {
            timestamp: Local::now(),
            level,
            message,
            style: None,
            typewriter: None,
        }
    }
}

/// The state manager for the TUI logger
///
/// This is the core component that manages all log messages, scrolling, cursor navigation,
/// visual selection, and typewriter effects. It should be stored in your main application
/// state and passed to the `TuiLoggerWidget` for rendering.
pub struct TuiLoggerState {
    // --- Core Data ---
    messages: VecDeque<LogMessage>,
    max_messages: usize,

    // --- Viewport & Scrolling ---
    /// Number of lines scrolled up from the bottom. 0 means view is at the bottom.
    scroll_offset: usize,
    /// When true, new messages snap the view to the bottom. Disabled by manual scrolling.
    autoscroll_enabled: bool,
    /// The height of the last rendered area, for page up/down calculations.
    last_view_height: usize,

    // --- Cursor & Selection ---
    cursor_enabled: bool,
    /// Visual line index (0-based from the first visual line of all messages)
    cursor_visual_line: usize,
    visual_mode_enabled: bool,
    /// Start of the visual selection, also a visual line index
    visual_selection_start_line: usize,

    // --- Visual Line Mapping ---
    /// Maps visual line index to (message_index, line_within_message)
    visual_line_map: Vec<(usize, usize)>,
    /// Total number of visual lines across all messages
    total_visual_lines: usize,
    /// Last calculated content width for visual line mapping
    last_content_width: usize,

    // --- Styling ---
    trace_style: Style,
    debug_style: Style,
    info_style: Style,
    warn_style: Style,
    error_style: Style,

    // --- Typewriter Effect ---
    /// Global setting for the typewriter effect.
    default_typewriter: TypewriterEffect,
    /// Messages waiting to be "typed out".
    typing_queue: VecDeque<LogMessage>,
    /// The message currently being typed. Holds the message, char index, and time of last char.
    current_typing_line: Option<(LogMessage, usize, Instant)>,
    /// Speed for typewriter effect in milliseconds per character
    typewriter_speed_millis: u64,
}

impl TuiLoggerState {
    pub fn new() -> Self {
        Self {
            messages: VecDeque::new(),
            max_messages: 10000,
            scroll_offset: 0,
            autoscroll_enabled: true,
            last_view_height: 0,
            cursor_enabled: false,
            cursor_visual_line: 0,
            visual_mode_enabled: false,
            visual_selection_start_line: 0,
            visual_line_map: Vec::new(),
            total_visual_lines: 0,
            last_content_width: 0,
            trace_style: Style::default().fg(Color::DarkGray),
            debug_style: Style::default().fg(Color::Blue),
            info_style: Style::default().fg(Color::Green),
            warn_style: Style::default().fg(Color::Yellow),
            error_style: Style::default().fg(Color::Red),
            default_typewriter: TypewriterEffect::default(),
            typing_queue: VecDeque::new(),
            current_typing_line: None,
            typewriter_speed_millis: 50, // Default 50ms per character
        }
    }

    /// Add a new log message to the state
    pub fn add_message(&mut self, message: LogMessage) {
        // Check if this message should use typewriter effect
        let should_type = message.typewriter.as_ref()
            .map(|t| t.enabled)
            .unwrap_or(self.default_typewriter.enabled);

        if should_type {
            self.typing_queue.push_back(message);
        } else {
            self.messages.push_back(message);
            
            // Maintain max message limit
            if self.messages.len() > self.max_messages {
                self.messages.pop_front();
            }
            
            // Auto-scroll to bottom if enabled
            if self.autoscroll_enabled {
                self.scroll_offset = 0;
            }

            // Mark visual line map as needing rebuild
            self.visual_line_map.clear();
        }
    }

    /// Scroll up by the specified amount (in visual lines)
    pub fn scroll_up(&mut self, amount: usize) {
        if self.total_visual_lines > 0 {
            let max_scroll = if self.total_visual_lines > self.last_view_height {
                self.total_visual_lines - self.last_view_height
            } else {
                0
            };
            self.scroll_offset = (self.scroll_offset + amount).min(max_scroll);
            self.autoscroll_enabled = false;
        }
    }

    /// Scroll down by the specified amount (in visual lines)
    pub fn scroll_down(&mut self, amount: usize) {
        self.scroll_offset = self.scroll_offset.saturating_sub(amount);
        if self.scroll_offset == 0 {
            self.autoscroll_enabled = true;
        }
    }

    /// Scroll up by one page
    pub fn page_up(&mut self) {
        self.scroll_up(self.last_view_height);
    }

    /// Scroll down by one page
    pub fn page_down(&mut self) {
        self.scroll_down(self.last_view_height);
    }

    /// Jump to the top of the log
    pub fn jump_to_top(&mut self) {
        if self.total_visual_lines > 0 {
            let max_scroll = if self.total_visual_lines > self.last_view_height {
                self.total_visual_lines - self.last_view_height
            } else {
                0
            };
            self.scroll_offset = max_scroll;
            self.autoscroll_enabled = false;
        }
    }

    /// Jump to the bottom of the log
    pub fn jump_to_bottom(&mut self) {
        self.scroll_offset = 0;
        self.autoscroll_enabled = true;
    }

    /// Clear all log messages
    pub fn clear_messages(&mut self) {
        self.messages.clear();
        self.scroll_offset = 0;
        self.autoscroll_enabled = true;
        self.cursor_visual_line = 0;
        self.visual_mode_enabled = false;
        self.visual_line_map.clear();
        self.total_visual_lines = 0;
    }

    /// Rebuild the visual line mapping based on current messages and content width
    fn rebuild_visual_line_map(&mut self, content_width: usize) {
        // Check if we need to rebuild at all
        if self.last_content_width == content_width &&
           self.visual_line_map.len() >= self.messages.len() {
            return; // No need to rebuild if width hasn't changed and we have enough mappings
        }

        let old_width = self.last_content_width;
        self.last_content_width = content_width;

        // Reserve space for timestamp and cursor indicator
        let timestamp_width = 13;
        let cursor_indicator_width = 2;
        let message_width = content_width.saturating_sub(timestamp_width + cursor_indicator_width);

        if message_width == 0 {
            self.visual_line_map.clear();
            self.total_visual_lines = 0;
            return;
        }

        // If width changed significantly, do a full rebuild
        if old_width != content_width {
            self.visual_line_map.clear();
            self.rebuild_full_visual_line_map(message_width);
        } else {
            // Incremental update: only add new messages
            self.rebuild_incremental_visual_line_map(message_width);
        }

        self.total_visual_lines = self.visual_line_map.len();
    }

    /// Full rebuild of visual line map (used when width changes)
    fn rebuild_full_visual_line_map(&mut self, message_width: usize) {
        // Collect message data first to avoid borrowing issues
        let messages_data: Vec<(usize, String)> = self.messages.iter()
            .enumerate()
            .map(|(idx, msg)| (idx, msg.message.clone()))
            .collect();

        for (msg_idx, message_text) in messages_data {
            self.add_message_to_visual_map(msg_idx, &message_text, message_width);
        }
    }

    /// Incremental rebuild: only add new messages to visual line map
    fn rebuild_incremental_visual_line_map(&mut self, message_width: usize) {
        // Find how many messages we've already mapped
        let mut mapped_messages = 0;
        for &(msg_idx, _) in &self.visual_line_map {
            mapped_messages = mapped_messages.max(msg_idx + 1);
        }

        // Collect new message data first to avoid borrowing issues
        let new_messages_data: Vec<(usize, String)> = (mapped_messages..self.messages.len())
            .filter_map(|msg_idx| {
                self.messages.get(msg_idx).map(|msg| (msg_idx, msg.message.clone()))
            })
            .collect();

        // Add any new messages
        for (msg_idx, message_text) in new_messages_data {
            self.add_message_to_visual_map(msg_idx, &message_text, message_width);
        }
    }

    /// Add a single message to the visual line map
    fn add_message_to_visual_map(&mut self, msg_idx: usize, message_text: &str, message_width: usize) {
        if message_text.len() <= message_width {
            // Single visual line - fast path
            self.visual_line_map.push((msg_idx, 0));
        } else {
            // Multiple visual lines - use optimized line counting
            let line_count = self.count_wrapped_lines(message_text, message_width);
            for line_idx in 0..line_count {
                self.visual_line_map.push((msg_idx, line_idx));
            }
        }
    }

    /// Fast line counting without actually creating the wrapped lines
    fn count_wrapped_lines(&self, text: &str, width: usize) -> usize {
        if width == 0 || text.is_empty() {
            return 1;
        }

        let mut line_count = 0;
        let mut current_line_len = 0;

        for word in text.split_whitespace() {
            let word_len = word.len();

            if current_line_len == 0 {
                // First word on line
                current_line_len = word_len;
                if line_count == 0 {
                    line_count = 1;
                }
            } else if current_line_len + 1 + word_len <= width {
                // Word fits on current line
                current_line_len += 1 + word_len;
            } else {
                // Word needs new line
                line_count += 1;
                current_line_len = word_len;
            }
        }

        line_count.max(1) // At least one line
    }



    // --- Cursor Navigation Methods ---

    /// Enable cursor mode for vi-like navigation
    pub fn enable_cursor_mode(&mut self) {
        self.cursor_enabled = true;
        self.autoscroll_enabled = false; // Disable autoscroll when cursor is active

        // Initialize cursor position to the last visual line
        if self.total_visual_lines > 0 {
            self.cursor_visual_line = self.total_visual_lines.saturating_sub(1);
        }
    }

    /// Disable cursor mode and return to normal scrolling
    pub fn disable_cursor_mode(&mut self) {
        self.cursor_enabled = false;
        self.visual_mode_enabled = false;
        self.autoscroll_enabled = true; // Re-enable autoscroll
    }

    /// Move cursor up one visual line
    pub fn cursor_up(&mut self) {
        if self.cursor_enabled && self.cursor_visual_line > 0 {
            self.cursor_visual_line -= 1;
            self.ensure_cursor_visible();
        }
    }

    /// Move cursor down one visual line
    pub fn cursor_down(&mut self) {
        if self.cursor_enabled && self.cursor_visual_line < self.total_visual_lines.saturating_sub(1) {
            self.cursor_visual_line += 1;
            self.ensure_cursor_visible();
        }
    }

    /// Move cursor to the top of the log (first visual line)
    pub fn cursor_to_top(&mut self) {
        if self.cursor_enabled {
            self.cursor_visual_line = 0;
            self.ensure_cursor_visible();
        }
    }

    /// Move cursor to the bottom of the log (last visual line)
    pub fn cursor_to_bottom(&mut self) {
        if self.cursor_enabled && self.total_visual_lines > 0 {
            self.cursor_visual_line = self.total_visual_lines - 1;
            self.ensure_cursor_visible();
        }
    }

    /// Ensure the cursor is visible in the current view
    fn ensure_cursor_visible(&mut self) {
        if !self.cursor_enabled || self.total_visual_lines == 0 {
            return;
        }

        let view_height = self.last_view_height;

        // Calculate the range of visible visual lines
        let visible_start = if self.total_visual_lines <= view_height {
            0
        } else if self.scroll_offset == 0 {
            // At bottom: show the last view_height lines
            self.total_visual_lines - view_height
        } else {
            // Scrolled up: show lines starting from (total_lines - view_height - scroll_offset)
            self.total_visual_lines.saturating_sub(view_height + self.scroll_offset)
        };

        let visible_end = (visible_start + view_height).min(self.total_visual_lines);

        // Adjust scroll if cursor is outside visible range
        if self.cursor_visual_line < visible_start {
            // Cursor is above visible area - scroll up
            let lines_to_scroll_up = visible_start - self.cursor_visual_line;
            self.scroll_offset += lines_to_scroll_up;
        } else if self.cursor_visual_line >= visible_end {
            // Cursor is below visible area - scroll down
            let lines_to_scroll_down = self.cursor_visual_line - visible_end + 1;
            self.scroll_offset = self.scroll_offset.saturating_sub(lines_to_scroll_down);
        }
    }

    // --- Visual Selection Methods ---

    /// Enter visual selection mode
    pub fn enter_visual_mode(&mut self) {
        if self.cursor_enabled {
            self.visual_mode_enabled = true;
            self.visual_selection_start_line = self.cursor_visual_line;
        }
    }

    /// Exit visual selection mode
    pub fn exit_visual_mode(&mut self) {
        self.visual_mode_enabled = false;
    }

    /// Check if visual mode is enabled
    pub fn is_visual_mode_enabled(&self) -> bool {
        self.visual_mode_enabled
    }

    /// Get the current visual selection range (start, end) inclusive in visual line indices
    pub fn get_visual_selection(&self) -> Option<(usize, usize)> {
        if self.visual_mode_enabled {
            let start = self.visual_selection_start_line.min(self.cursor_visual_line);
            let end = self.visual_selection_start_line.max(self.cursor_visual_line);
            Some((start, end))
        } else {
            None
        }
    }

    /// Copy selected messages to clipboard and exit visual mode
    pub fn copy_selection(&mut self) -> Option<String> {
        if let Some((start_visual_line, end_visual_line)) = self.get_visual_selection() {
            let mut result = String::new();
            let mut last_msg_idx = None;

            // Iterate through selected visual lines
            for visual_line_idx in start_visual_line..=end_visual_line {
                if let Some(&(msg_idx, _line_within_msg)) = self.visual_line_map.get(visual_line_idx) {
                    // Only add the full message once per logical message
                    if last_msg_idx != Some(msg_idx) {
                        if let Some(message) = self.messages.get(msg_idx) {
                            result.push_str(&format!("{:02}:{:02}:{:02}.{:03} [{}] {}\n",
                                message.timestamp.hour(),
                                message.timestamp.minute(),
                                message.timestamp.second(),
                                message.timestamp.nanosecond() / 1_000_000,
                                match message.level {
                                    LogLevel::Trace => "TRACE",
                                    LogLevel::Debug => "DEBUG",
                                    LogLevel::Info => "INFO",
                                    LogLevel::Warn => "WARN",
                                    LogLevel::Error => "ERROR",
                                    LogLevel::Custom(ref s) => s,
                                },
                                message.message
                            ));
                            last_msg_idx = Some(msg_idx);
                        }
                    }
                }
            }

            if !result.is_empty() {
                // Copy to clipboard using the existing function
                copy_to_clipboard(result.clone());
                // Exit visual mode after copying
                self.exit_visual_mode();
                Some(result)
            } else {
                None
            }
        } else {
            None
        }
    }

    // --- Typewriter Effect Methods ---

    /// Start typewriter effect for a specific message
    pub fn start_typewriter(&mut self, message_index: usize) {
        if let Some(message) = self.messages.get(message_index).cloned() {
            if message.typewriter.as_ref().map_or(false, |t| t.enabled) {
                let speed = message.typewriter.as_ref().map_or(50, |t| t.speed_millis);
                self.current_typing_line = Some((message, 0, Instant::now()));
                self.typewriter_speed_millis = speed;
            }
        }
    }

    /// Update typewriter animation - should be called regularly from the main loop
    pub fn update_typewriter(&mut self) -> bool {
        if let Some((ref message, ref mut char_index, ref mut last_update)) = self.current_typing_line {
            let now = Instant::now();
            let elapsed = now.duration_since(*last_update).as_millis() as u64;

            if elapsed >= self.typewriter_speed_millis {
                *char_index += 1;
                *last_update = now;

                // Check if we've finished typing this message
                if *char_index >= message.message.chars().count() {
                    self.current_typing_line = None;
                    return false; // Animation complete
                }
                return true; // Animation continuing
            }
        }
        false // No animation or not time to update
    }

    /// Get the currently visible portion of a typewriter message
    pub fn get_typewriter_text(&self, message_index: usize) -> Option<String> {
        if let Some((ref typing_message, char_index, _)) = self.current_typing_line {
            if let Some(message) = self.messages.get(message_index) {
                if std::ptr::eq(message, typing_message) {
                    let chars: Vec<char> = message.message.chars().collect();
                    let visible_chars = chars.iter().take(char_index).collect::<String>();
                    return Some(visible_chars);
                }
            }
        }
        None
    }

    /// Check if a message is currently being typed
    pub fn is_message_typing(&self, message_index: usize) -> bool {
        if let Some((ref typing_message, _, _)) = self.current_typing_line {
            if let Some(message) = self.messages.get(message_index) {
                return std::ptr::eq(message, typing_message);
            }
        }
        false
    }

    /// Add a message with typewriter effect
    pub fn add_typewriter_message(&mut self, level: LogLevel, message: String, speed_millis: u64) {
        let typewriter_effect = TypewriterEffect {
            enabled: true,
            speed_millis,
        };

        let log_message = LogMessage {
            timestamp: Local::now(),
            level,
            message,
            style: None,
            typewriter: Some(typewriter_effect),
        };

        self.add_message(log_message);

        // Start typewriter effect for the newly added message
        if !self.messages.is_empty() {
            let message_index = self.messages.len() - 1;
            self.start_typewriter(message_index);
        }
    }

    /// Stop current typewriter animation
    pub fn stop_typewriter(&mut self) {
        self.current_typing_line = None;
    }

    /// Get the style for a given log level
    fn get_style_for_level(&self, level: &LogLevel) -> Style {
        match level {
            LogLevel::Trace => self.trace_style,
            LogLevel::Debug => self.debug_style,
            LogLevel::Info => self.info_style,
            LogLevel::Warn => self.warn_style,
            LogLevel::Error => self.error_style,
            LogLevel::Custom(_) => Style::default(),
        }
    }

    /// Create a darker version of a color for visual selection highlighting
    fn darken_color(color: Color) -> Color {
        match color {
            Color::Rgb(r, g, b) => {
                // Darken by 25% (multiply by 0.75)
                Color::Rgb(
                    (r as f32 * 0.75) as u8,
                    (g as f32 * 0.75) as u8,
                    (b as f32 * 0.75) as u8,
                )
            }
            Color::Red => Color::Rgb(191, 0, 0),      // 75% of 255
            Color::Green => Color::Rgb(0, 191, 0),
            Color::Blue => Color::Rgb(0, 0, 191),
            Color::Yellow => Color::Rgb(191, 191, 0),
            Color::Magenta => Color::Rgb(191, 0, 191),
            Color::Cyan => Color::Rgb(0, 191, 191),
            Color::Gray => Color::Rgb(127, 127, 127),
            Color::DarkGray => Color::Rgb(84, 84, 84),
            Color::LightRed => Color::Rgb(191, 127, 127),
            Color::LightGreen => Color::Rgb(127, 191, 127),
            Color::LightBlue => Color::Rgb(127, 127, 191),
            Color::LightYellow => Color::Rgb(191, 191, 127),
            Color::LightMagenta => Color::Rgb(191, 127, 191),
            Color::LightCyan => Color::Rgb(127, 191, 191),
            Color::White => Color::Rgb(191, 191, 191),
            Color::Black => Color::Rgb(0, 0, 0),
            Color::Reset => Color::Reset,
            Color::Indexed(i) => Color::Indexed(i), // Can't easily darken indexed colors
        }
    }
}

/// The widget for rendering the TUI logger
pub struct TuiLoggerWidget<'a> {
    block: Option<Block<'a>>,
}

impl<'a> TuiLoggerWidget<'a> {
    pub fn new() -> Self {
        Self { block: None }
    }

    pub fn block(mut self, block: Block<'a>) -> Self {
        self.block = Some(block);
        self
    }

    /// Calculate all visible lines with proper wrapping and formatting
    fn calculate_visible_lines(&self, state: &mut TuiLoggerState, _content_height: usize, content_width: usize) -> Vec<Line<'static>> {
        // Rebuild visual line map if needed
        state.rebuild_visual_line_map(content_width);

        let mut lines = Vec::new();

        // Reserve space for timestamp (format: "HH:MM:SS.mmm ") and cursor indicator ("> ")
        let timestamp_width = 13;
        let cursor_indicator_width = 2;
        let message_width = content_width.saturating_sub(timestamp_width + cursor_indicator_width);

        // Process each visual line
        for (visual_line_idx, &(msg_idx, line_within_msg)) in state.visual_line_map.iter().enumerate() {
            if let Some(message) = state.messages.get(msg_idx) {
                let mut style = message.style.unwrap_or_else(|| state.get_style_for_level(&message.level));

                // Apply cursor and visual selection highlighting based on visual line
                let is_cursor_line = state.cursor_enabled && visual_line_idx == state.cursor_visual_line;
                let is_selected = if let Some((start, end)) = state.get_visual_selection() {
                    visual_line_idx >= start && visual_line_idx <= end
                } else {
                    false
                };

                // Modify style for cursor and selection
                if is_cursor_line {
                    // Use a very dark gray that's closer to black background for better readability
                    style = style.bg(Color::Rgb(25, 25, 25));
                }
                if is_selected {
                    // Use the same color as normal highlighting but 25% darker
                    let original_fg = style.fg.unwrap_or(Color::White);
                    let darkened_bg = TuiLoggerState::darken_color(original_fg);
                    style = style.bg(darkened_bg);
                }

                // Format timestamp more precisely (only for first line of each message)
                let (timestamp_span, cursor_indicator) = if line_within_msg == 0 {
                    let timestamp_str = format!("{:02}:{:02}:{:02}.{:03} ",
                        message.timestamp.hour(),
                        message.timestamp.minute(),
                        message.timestamp.second(),
                        message.timestamp.nanosecond() / 1_000_000
                    );
                    let mut timestamp_style = Style::default().fg(Color::DarkGray);
                    if is_cursor_line || is_selected {
                        timestamp_style = timestamp_style.bg(style.bg.unwrap_or(Color::Reset));
                    }
                    let timestamp_span = Span::styled(timestamp_str, timestamp_style);

                    // Add cursor indicator
                    let cursor_indicator = if is_cursor_line {
                        let cursor_color = if state.visual_mode_enabled {
                            Color::Blue
                        } else {
                            Color::Yellow
                        };
                        Span::styled("> ", Style::default().fg(cursor_color).bg(style.bg.unwrap_or(Color::Reset)))
                    } else {
                        Span::styled("  ", Style::default().bg(style.bg.unwrap_or(Color::Reset)))
                    };

                    (timestamp_span, cursor_indicator)
                } else {
                    // Continuation lines get indentation
                    let indent = " ".repeat(timestamp_width + 2); // +2 for cursor indicator
                    let indent_span = Span::styled(indent, Style::default().bg(style.bg.unwrap_or(Color::Reset)));
                    let empty_cursor = Span::styled("", Style::default());
                    (indent_span, empty_cursor)
                };

                // Get the message text (potentially partial for typewriter effect)
                let display_message = if let Some(typewriter_text) = state.get_typewriter_text(msg_idx) {
                    typewriter_text
                } else {
                    message.message.clone()
                };

                // Add typewriter cursor if this message is currently being typed
                let final_message = if state.is_message_typing(msg_idx) {
                    format!("{}█", display_message) // Add typing cursor
                } else {
                    display_message
                };

                // Get the specific wrapped line for this visual line
                let wrapped_lines = self.wrap_text(&final_message, message_width);
                if let Some(wrapped_line) = wrapped_lines.get(line_within_msg) {
                    let message_span = Span::styled(wrapped_line.clone(), style);
                    if line_within_msg == 0 {
                        lines.push(Line::from(vec![cursor_indicator, timestamp_span, message_span]));
                    } else {
                        lines.push(Line::from(vec![timestamp_span, message_span]));
                    }
                }
            }
        }

        lines
    }

    /// Wrap text to fit within specified width
    fn wrap_text(&self, text: &str, width: usize) -> Vec<String> {
        if width == 0 {
            return vec![text.to_string()];
        }

        let mut lines = Vec::new();
        let mut current_line = String::new();

        for word in text.split_whitespace() {
            if current_line.is_empty() {
                current_line = word.to_string();
            } else if current_line.len() + 1 + word.len() <= width {
                current_line.push(' ');
                current_line.push_str(word);
            } else {
                lines.push(current_line);
                current_line = word.to_string();
            }
        }

        if !current_line.is_empty() {
            lines.push(current_line);
        }

        if lines.is_empty() {
            lines.push(String::new());
        }

        lines
    }


}

impl<'a> Default for TuiLoggerWidget<'a> {
    fn default() -> Self {
        Self::new()
    }
}

impl<'a> StatefulWidget for TuiLoggerWidget<'a> {
    type State = TuiLoggerState;

    fn render(self, area: Rect, buf: &mut Buffer, state: &mut Self::State) {
        // Calculate content area
        let content_area = match &self.block {
            Some(block) => {
                block.clone().render(area, buf);
                block.inner(area)
            }
            None => area,
        };

        let content_height = content_area.height as usize;
        let content_width = content_area.width as usize;
        state.last_view_height = content_height;

        // Create lines for rendering with enhanced formatting
        let mut lines = Vec::new();

        // Calculate visible message range
        if !state.messages.is_empty() && content_height > 0 {
            // Calculate all visible lines first
            let visible_lines = self.calculate_visible_lines(state, content_height, content_width);
            let total_lines = visible_lines.len();

            if total_lines > 0 {
                // Simple scrolling: show the last content_height lines, offset by scroll_offset
                let start_line = if total_lines <= content_height {
                    // If we have fewer lines than screen height, show all from the beginning
                    0
                } else if state.scroll_offset == 0 {
                    // At bottom: show the last content_height lines
                    total_lines - content_height
                } else {
                    // Scrolled up: show lines starting from (total_lines - content_height - scroll_offset)
                    total_lines.saturating_sub(content_height + state.scroll_offset)
                };

                let end_line = (start_line + content_height).min(total_lines);

                // Render visible lines
                for line_idx in start_line..end_line {
                    if let Some(rendered_line) = visible_lines.get(line_idx) {
                        lines.push(rendered_line.clone());
                    }
                }
            }
        }



        // Render the paragraph with word wrapping
        let paragraph = Paragraph::new(Text::from(lines))
            .wrap(Wrap { trim: false });
        paragraph.render(content_area, buf);
    }
}

/// Global logger channel
static LOGGER_CHANNEL: OnceCell<mpsc::Sender<LogMessage>> = OnceCell::new();

/// Initialize the TUI logger system
pub fn init() -> mpsc::Receiver<LogMessage> {
    let (tx, rx) = mpsc::channel(256);
    LOGGER_CHANNEL.set(tx).expect("TUI Logger already initialized");

    let logger = TuiLogger::new();
    log::set_max_level(log::LevelFilter::Trace);
    log::set_boxed_logger(Box::new(logger)).expect("Failed to set logger");
    
    rx
}

/// The global logger implementation
struct TuiLogger;

impl TuiLogger {
    fn new() -> Self {
        Self
    }
}

impl Log for TuiLogger {
    fn enabled(&self, metadata: &Metadata) -> bool {
        // Only show logs from our application crate
        let crate_name = env!("CARGO_PKG_NAME");
        metadata.target().starts_with(crate_name)
    }

    fn log(&self, record: &Record) {
        if self.enabled(record.metadata()) {
            let log_message = LogMessage::new(
                record.level().into(),
                format!("{}", record.args()),
            );

            if let Some(sender) = LOGGER_CHANNEL.get() {
                // Use try_send to avoid blocking if the channel is full
                let _ = sender.try_send(log_message);
            }
        }
    }

    fn flush(&self) {
        // No-op for our implementation
    }
}

// --- Custom Logging Macros ---

/// Enhanced info logging with optional typewriter effect
#[macro_export]
macro_rules! tui_info {
    ($($arg:tt)*) => {
        log::info!($($arg)*);
    };
}

/// Enhanced debug logging with optional typewriter effect
#[macro_export]
macro_rules! tui_debug {
    ($($arg:tt)*) => {
        log::debug!($($arg)*);
    };
}

/// Enhanced error logging with optional typewriter effect
#[macro_export]
macro_rules! tui_error {
    ($($arg:tt)*) => {
        log::error!($($arg)*);
    };
}

/// Enhanced warn logging with optional typewriter effect
#[macro_export]
macro_rules! tui_warn {
    ($($arg:tt)*) => {
        log::warn!($($arg)*);
    };
}

/// Enhanced trace logging with optional typewriter effect
#[macro_export]
macro_rules! tui_trace {
    ($($arg:tt)*) => {
        log::trace!($($arg)*);
    };
}

/// Log with typewriter effect
#[macro_export]
macro_rules! tui_typewriter {
    ($level:expr, $speed:expr, $($arg:tt)*) => {
        {
            use $crate::tui::tui_logger::{LogLevel, LogMessage, TypewriterEffect, LOGGER_CHANNEL};
            use chrono::Local;

            let message = format!($($arg)*);
            let typewriter_effect = TypewriterEffect {
                enabled: true,
                speed_millis: $speed,
            };

            let log_message = LogMessage {
                timestamp: Local::now(),
                level: $level,
                message,
                style: None,
                typewriter: Some(typewriter_effect),
            };

            if let Some(sender) = LOGGER_CHANNEL.get() {
                let _ = sender.try_send(log_message);
            }
        }
    };
}

/// Log info with typewriter effect
#[macro_export]
macro_rules! tui_typewriter_info {
    ($speed:expr, $($arg:tt)*) => {
        tui_typewriter!($crate::tui::tui_logger::LogLevel::Info, $speed, $($arg)*);
    };
}

/// Log debug with typewriter effect
#[macro_export]
macro_rules! tui_typewriter_debug {
    ($speed:expr, $($arg:tt)*) => {
        tui_typewriter!($crate::tui::tui_logger::LogLevel::Debug, $speed, $($arg)*);
    };
}

/// Log error with typewriter effect
#[macro_export]
macro_rules! tui_typewriter_error {
    ($speed:expr, $($arg:tt)*) => {
        tui_typewriter!($crate::tui::tui_logger::LogLevel::Error, $speed, $($arg)*);
    };
}

/// Log warn with typewriter effect
#[macro_export]
macro_rules! tui_typewriter_warn {
    ($speed:expr, $($arg:tt)*) => {
        tui_typewriter!($crate::tui::tui_logger::LogLevel::Warn, $speed, $($arg)*);
    };
}

/// Log trace with typewriter effect
#[macro_export]
macro_rules! tui_typewriter_trace {
    ($speed:expr, $($arg:tt)*) => {
        tui_typewriter!($crate::tui::tui_logger::LogLevel::Trace, $speed, $($arg)*);
    };
}

/// Log with custom styling
#[macro_export]
macro_rules! tui_styled {
    ($level:expr, $style:expr, $($arg:tt)*) => {
        {
            use $crate::tui::tui_logger::{LogLevel, LogMessage, LOGGER_CHANNEL};
            use chrono::Local;

            let message = format!($($arg)*);

            let log_message = LogMessage {
                timestamp: Local::now(),
                level: $level,
                message,
                style: Some($style),
                typewriter: None,
            };

            if let Some(sender) = LOGGER_CHANNEL.get() {
                let _ = sender.try_send(log_message);
            }
        }
    };
}

/// Log with both custom styling and typewriter effect
#[macro_export]
macro_rules! tui_styled_typewriter {
    ($level:expr, $style:expr, $speed:expr, $($arg:tt)*) => {
        {
            use $crate::tui::tui_logger::{LogLevel, LogMessage, TypewriterEffect, LOGGER_CHANNEL};
            use chrono::Local;

            let message = format!($($arg)*);
            let typewriter_effect = TypewriterEffect {
                enabled: true,
                speed_millis: $speed,
            };

            let log_message = LogMessage {
                timestamp: Local::now(),
                level: $level,
                message,
                style: Some($style),
                typewriter: Some(typewriter_effect),
            };

            if let Some(sender) = LOGGER_CHANNEL.get() {
                let _ = sender.try_send(log_message);
            }
        }
    };
}

/// Log a custom message with custom level
#[macro_export]
macro_rules! tui_custom {
    ($level_name:expr, $($arg:tt)*) => {
        {
            use $crate::tui::tui_logger::{LogLevel, LogMessage, LOGGER_CHANNEL};
            use chrono::Local;

            let message = format!($($arg)*);

            let log_message = LogMessage {
                timestamp: Local::now(),
                level: LogLevel::Custom($level_name.to_string()),
                message,
                style: None,
                typewriter: None,
            };

            if let Some(sender) = LOGGER_CHANNEL.get() {
                let _ = sender.try_send(log_message);
            }
        }
    };
}
