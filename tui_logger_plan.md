# Plan: Custom TUI Logger Implementation

## 1. Project Goal & Analysis

### 1.1. Objective

The primary goal is to replace the `tui-logger` crate with a bespoke, high-performance, and feature-rich TUI logging widget. The current crate is not flexible enough for our needs. The new solution will be built from scratch using `ratatui` and integrated seamlessly into the existing interactive application flow.

### 1.2. Pain Points of `tui-logger`

- **Limited Control:** We have minimal control over rendering, styling, and internal state.
- **No Cursor/Selection:** Lacks advanced features like a movable cursor, visual line selection, and copying.
- **Clunky Scrolling:** Scrolling is managed via abstract events, offering less direct control.
- **No Advanced Rendering:** Cannot support dynamic effects like the requested "typewriter" effect.

### 1.3. Analysis of Existing Integration

The new logger must replace functionality currently found in these key areas:

- **`src/interactive/app.rs`**: The `InteractiveApp` struct holds `log_state: TuiWidgetState`. This will be replaced with our new custom state struct.
- **`src/interactive/ui.rs`**: The `draw_lledit_log_view` function configures and renders the `TuiLoggerWidget`. This function will be rewritten to use our new custom widget. It currently customizes the block, title, and styles based on the app's state (e.g., `is_processing`), which our new widget must also support.
- **`src/interactive/key_handler.rs` & `runner.rs`**: Scrolling is handled by `app.log_state.transition(TuiWidgetEvent::...)`. This will be replaced with direct method calls on our new state struct, e.g., `app.logger_state.scroll_down()`.
- **Global `log` Macros**: The application uses standard `log::info!`, `log::debug!`, etc. Our solution must implement the `log::Log` trait to capture these calls without requiring changes throughout the codebase.

## 2. High-Level Architecture

The new logger will consist of three main components, located in a new `src/tui/tui_logger.rs` file.

1. **`TuiLogger` (The `log::Log` Trait Implementation)**: A struct that acts as the global logger. It captures all `log::*` macro calls from any thread and sends them over a channel to the TUI thread.
2. **`TuiLoggerState` (The State)**: The "brain" of the logger. It holds all log messages, manages the viewport (scrolling), cursor position, visual selection, and typewriter effect state. This will live inside `InteractiveApp`.
3. **`TuiLoggerWidget` (The View)**: A `ratatui::widgets::StatefulWidget` that knows how to render the `TuiLoggerState` to the screen within a given `Rect`.

Communication will flow from `log::*` macros -> `TuiLogger` -> `mpsc::channel` -> `interactive::runner` loop -> `TuiLoggerState` -> `TuiLoggerWidget`.

---

## 3. Detailed Component Design

### 3.1. `src/tui/tui_logger.rs` - Core Structs

#### 3.1.1. `LogMessage` Struct

This struct will represent a single, styled log entry.

```rust
// in src/tui/tui_logger.rs

use ratatui::style::{Color, Style};
use std::time::SystemTime;

#[derive(Clone, Debug)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
    Custom(String), // For special-purpose logs
}

#[derive(Clone, Debug)]
pub struct TypewriterEffect {
    pub enabled: bool,
    pub speed_millis: u64, // Milliseconds per character
}

#[derive(Clone, Debug)]
pub struct LogMessage {
    pub timestamp: SystemTime,
    pub level: LogLevel,
    pub message: String,
    
    // Styling overrides for custom logs
    pub style: Option<Style>,
    pub typewriter: Option<TypewriterEffect>,
}
```

#### 3.1.2. `TuiLoggerState` Struct

This holds the complete state and logic for the logger. It will replace `TuiWidgetState` in `InteractiveApp`.

```rust
// in src/tui/tui_logger.rs

use std::collections::VecDeque;

pub struct TuiLoggerState {
    // --- Core Data ---
    messages: VecDeque<LogMessage>,
    max_messages: usize,

    // --- Viewport & Scrolling ---
    /// Number of lines scrolled up from the bottom. 0 means view is at the bottom.
    scroll_offset: usize,
    /// When true, new messages snap the view to the bottom. Disabled by manual scrolling.
    autoscroll_enabled: bool,
    /// The height of the last rendered area, for page up/down calculations.
    last_view_height: usize,

    // --- Cursor & Selection ---
    cursor_enabled: bool,
    /// Index from the *start* of the `messages` VecDeque.
    cursor_position: usize,
    visual_mode_enabled: bool,
    /// Start of the visual selection, also an index from the start of the VecDeque.
    visual_selection_start: usize,

    // --- Styling ---
    trace_style: Style,
    debug_style: Style,
    info_style: Style,
    warn_style: Style,
    error_style: Style,

    // --- Typewriter Effect ---
    /// Global setting for the typewriter effect.
    default_typewriter: TypewriterEffect,
    /// Messages waiting to be "typed out".
    typing_queue: VecDeque<LogMessage>,
    /// The message currently being typed. Holds the message, char index, and time of last char.
    current_typing_line: Option<(LogMessage, usize, std::time::Instant)>,
}
```

#### 3.1.3. `TuiLoggerWidget` Struct

The `ratatui` widget responsible for rendering.

```rust
// in src/tui/tui_logger.rs

use ratatui::widgets::{Block, StatefulWidget, Paragraph};
use ratatui::text::{Line, Span, Text};
use ratatui::layout::Rect;
use ratatui::Frame;

pub struct TuiLoggerWidget<'a> {
    block: Option<Block<'a>>,
    // More styling options can be added here, e.g., for timestamps.
}

impl<'a> StatefulWidget for TuiLoggerWidget<'a> {
    type State = TuiLoggerState;

    fn render(self, area: Rect, buf: &mut ratatui::buffer::Buffer, state: &mut Self::State) {
        // ... Detailed rendering logic ...
    }
}
```

### 3.2. Global Logger and Channel

We need a way to capture logs from anywhere and send them to the TUI thread.

```rust
// in src/tui/tui_logger.rs

use once_cell::sync::OnceCell; // Add to Cargo.toml
use tokio::sync::mpsc;
use log::{Log, Record, Level, Metadata};

static LOGGER_CHANNEL: OnceCell<mpsc::Sender<LogMessage>> = OnceCell::new();

pub fn init() -> mpsc::Receiver<LogMessage> {
    let (tx, rx) = mpsc::channel(256);
    LOGGER_CHANNEL.set(tx).expect("TUI Logger already initialized");

    let logger = TuiLogger::new();
    // It's crucial to set the max level here to what you want to capture.
    log::set_max_level(log::LevelFilter::Trace); 
    log::set_boxed_logger(Box::new(logger)).expect("Failed to set logger");
    
    rx
}

struct TuiLogger;
// ... impl TuiLogger, and impl log::Log for TuiLogger ...
```

## 4. Step-by-Step Implementation Plan

### Step 1: Foundational Setup

1. **Create Files**: Create `src/tui/mod.rs` and `src/tui/tui_logger.rs`.
2. **Dependencies**: Add `once_cell = "1.19"` to `Cargo.toml`. Remove `tui-logger`.
3. **Define Structs**: Implement the empty structs `LogMessage`, `TuiLoggerState`, and `TuiLoggerWidget` as defined above in `src/tui/tui_logger.rs`.
4. **Implement `TuiLoggerState::new()`**:
    - Initialize `messages` with an empty `VecDeque`.
    - Set `max_messages` to `10000`.
    - Initialize `scroll_offset = 0`, `autoscroll_enabled = true`.
    - Initialize `cursor_enabled = false`, `cursor_position = 0`.
    - Initialize `visual_mode_enabled = false`, `visual_selection_start = 0`.
    - Set default `Style`s for each log level (e.g., `Style::default().fg(Color::Green)` for Info).
    - Initialize `default_typewriter` to be disabled.
    - Initialize `typing_queue` and `current_typing_line`.
5. **Implement Global Logger (`TuiLogger`)**:
    - Implement `TuiLogger::new()`.
    - Implement `impl log::Log for TuiLogger`.
    - The `log` method will convert `&log::Record` into our `LogMessage` struct.
    - It will then use `LOGGER_CHANNEL.get().unwrap().blocking_send(log_message)`. `blocking_send` is appropriate for a separate logging thread.
6. **Integrate into `InteractiveApp`**:
    - In `interactive/app.rs`, replace `pub log_state: TuiWidgetState` with `pub logger_state: TuiLoggerState`.
    - In `InteractiveApp::new()`, initialize it with `TuiLoggerState::new()`.
    - In `runner.rs`, call `tui::tui_logger::init()` at the start to get the `mpsc::Receiver`.
    - In the main loop `tokio::select!`, add a branch to `recv()` from the logger receiver.
    - When a `LogMessage` is received, call a new method `TuiLoggerState::add_message(message)`.
        - This method pushes to the back of the `messages` `VecDeque`.
        - If `messages.len() > max_messages`, it calls `messages.pop_front()`.
        - If `autoscroll_enabled` is true, it resets `scroll_offset` to 0.

### Step 2: Basic Rendering & Scrolling

1. **Implement `TuiLoggerWidget::render()` (Basic)**:
    - Calculate `content_height` (`area.height.saturating_sub(2)` if a block is present).
    - Store `content_height` in `state.last_view_height`.
    - Calculate the visible range of messages from the `state.messages` `VecDeque` using `state.scroll_offset`. The range will be from `(messages.len() - scroll_offset - content_height)` to `(messages.len() - scroll_offset)`. Handle underflow and edge cases.
    - For each visible message, create a `ratatui::text::Line` with a `Span` for the timestamp and a `Span` for the message content, styled according to the message's level.
    - Render the lines using a `Paragraph` widget.
2. **Implement `TuiLoggerState` Scrolling Methods**:
    - `scroll_up(&mut self, amount: usize)`: Increments `scroll_offset`, clamped to `messages.len() - 1`. Sets `autoscroll_enabled = false`.
    - `scroll_down(&mut self, amount: usize)`: Decrements `scroll_offset`, clamped to `0`.
    - `page_up(&mut self)`: Calls `scroll_up(self.last_view_height)`.
    - `page_down(&mut self)`: Calls `scroll_down(self.last_view_height)`.
    - `jump_to_top(&mut self)`: Sets `scroll_offset = self.messages.len() - 1`. Sets `autoscroll_enabled = false`.
    - `jump_to_bottom(&mut self)`: Sets `scroll_offset = 0`. Sets `autoscroll_enabled = true`.
3. **Update `key_handler.rs`**: Replace old `TuiWidgetEvent` transitions with calls to these new methods (e.g., `PageUp` key calls `app.logger_state.page_up()`).

### Step 3: Cursor Mode & Visual Selection

1. **Implement Cursor Methods in `TuiLoggerState`**:
    - `toggle_cursor(&mut self)`: Flips `self.cursor_enabled`. If enabling, set `cursor_position` to the last message index.
    - `move_cursor_up(&mut self)`: Decrements `cursor_position`, clamped to `0`. Adjusts `scroll_offset` to keep the cursor in view.
    - `move_cursor_down(&mut self)`: Increments `cursor_position`, clamped to the last index. Adjusts `scroll_offset`.
    - `cursor_to_top(&mut self)`: Sets `cursor_position = 0`.
    - `cursor_to_bottom(&mut self)`: Sets `cursor_position = self.messages.len() - 1`.
2. **Implement Visual Mode Methods**:
    - `toggle_visual_mode(&mut self)`: Flips `self.visual_mode_enabled`. If enabling, sets `visual_selection_start = self.cursor_position`.
    - `get_selected_text(&self) -> String`: If in visual mode, gets the range of lines between `visual_selection_start` and `cursor_position`, joins their text, and returns it.
3. **Update `TuiLoggerWidget::render()`**:
    - Inside the loop that creates `Line`s, check if the current line's index matches `state.cursor_position` and `state.cursor_enabled`. If so, apply a highlight style (e.g., `bg(Color::DarkGray)`).
    - Also check if the line's index is within the visual selection range. If so, apply a different highlight (e.g., `bg(Color::Blue)`).
4. **Update `key_handler.rs`**: When the log view is "focused" (this will be a new `InputMode` or a flag in `InteractiveApp`):
    - `j`/`k`/Arrows call `move_cursor_up`/`down`.
    - `gg` calls `cursor_to_top`.
    - `G` calls `cursor_to_bottom`.
    - `v` calls `toggle_visual_mode`.
    - `y` calls `get_selected_text` and copies the result to the clipboard using the existing `commands_utils::copy_to_clipboard`.

### Step 4: Typewriter Effect

1. **Modify `TuiLoggerState::add_message`**:
    - If the incoming message's typewriter effect is enabled (or the global default is), push it to `self.typing_queue` instead of `self.messages`.
    - Otherwise, push it directly to `self.messages`.
2. **Update `runner.rs` `Tick` Handler**:
    - Call a new method `app.logger_state.update_typing_effect()`.
3. **Implement `TuiLoggerState::update_typing_effect()`**:
    - If `self.current_typing_line.is_none()`, try to pop a message from `self.typing_queue`. If successful, set `self.current_typing_line = Some((message, 0, Instant::now()))`.
    - If `self.current_typing_line` is `Some((message, char_idx, last_instant))`:
        - Check if `last_instant.elapsed()` is greater than the message's typewriter speed.
        - If so, increment `char_idx`.
        - If `char_idx` reaches the end of `message.message`, move the completed `message` to `self.messages`, set `self.current_typing_line = None`, and if autoscroll is on, jump to the bottom.
4. **Update `TuiLoggerWidget::render()`**:
    - After rendering the main block of messages from `state.messages`, check if `state.current_typing_line` is `Some`.
    - If so, render one more `Line` at the bottom. This line's content will be a substring of the full message, from `0` to the current `char_idx`.
    - This ensures the "typing" line appears below the historical, fully-rendered logs.

### Step 5: Custom Log Macro

1. **Define `tui_log!` macro in `tui_logger.rs`**:

    ```rust
    // in src/tui/tui_logger.rs
    
    #[macro_export]
    macro_rules! tui_log {
        ($level:expr, style: $style:expr, message: $($arg:tt)*) => {
            // ... logic to send LogMessage with style override
        };
        ($level:expr, message: $($arg:tt)*) => {
            // ... logic to send LogMessage with default style for level
        };
        // Add more variants as needed, e.g., for typewriter overrides
    }
    ```

2. **Implement the Macro Logic**:
    - The macro will construct a `LogMessage` struct with the provided parameters.
    - The message part will be created using `format!($($arg)*)`.
    - It will then get the global channel sender via `LOGGER_CHANNEL.get()` and send the message, similar to the `log::Log` implementation.

### Step 6: Final Integration and Cleanup

1. **Refactor `draw_lledit_log_view`**: This function in `ui.rs` will be much simpler. It will now just create an instance of `TuiLoggerWidget`, set its `Block` based on the app's state (as it does now), and call `frame.render_stateful_widget`.
2. **Remove Old Code**: Delete `TuiWidgetState` from `app.rs`, `TuiLoggerWidget` usage from `ui.rs`, and `TuiWidgetEvent` calls from `key_handler.rs`.
3. **Documentation**: Add doc comments to the new public structs and methods in `src/tui/tui_logger.rs` explaining how to use them.
4. **Configuration**: The `TuiLoggerState::new()` function can be modified to take a configuration struct, allowing colors and default typewriter settings to be loaded from `AppConfig`.

---

This comprehensive plan covers the creation of a new, powerful TUI logger from the ground up. By following these steps, we can build a flexible and feature-complete component that perfectly fits the application's needs, providing a much-improved user experience over the existing `tui-logger` crate.
